# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-request-user-attention"
description = "Enables the request_user_attention command without any pre-configured scope."
commands.allow = ["request_user_attention"]

[[permission]]
identifier = "deny-request-user-attention"
description = "Denies the request_user_attention command without any pre-configured scope."
commands.deny = ["request_user_attention"]
