{"rustc": 1842507548689473721, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 15599728179509752985, "path": 12257077738785796254, "deps": [[5103565458935487, "futures_io", false, 2472899571327784955], [1615478164327904835, "pin_utils", false, 10845793856787272542], [1906322745568073236, "pin_project_lite", false, 8161958276441776399], [5451793922601807560, "slab", false, 11875787285946783543], [7013762810557009322, "futures_sink", false, 469913744781824817], [7620660491849607393, "futures_core", false, 4374087354944172571], [15932120279885307830, "memchr", false, 6398370832424983751], [16240732885093539806, "futures_task", false, 10268939351816740764]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-7583bc0fdbeea443\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}