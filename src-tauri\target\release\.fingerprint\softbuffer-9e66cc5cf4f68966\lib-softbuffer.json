{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2040997289075261528, "path": 4764123382064906521, "deps": [[376837177317575824, "build_script_build", false, 4232668004367839526], [4143744114649553716, "raw_window_handle", false, 13056677237480622340], [5986029879202738730, "log", false, 8722473586600162715], [10281541584571964250, "windows_sys", false, 17703432856835532237]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-9e66cc5cf4f68966\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}