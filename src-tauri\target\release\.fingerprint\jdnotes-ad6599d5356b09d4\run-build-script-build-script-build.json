{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17690913698614214024, "build_script_build", false, 13278504681095700170], [12092653563678505622, "build_script_build", false, 1741793072285732969], [8324462083842905811, "build_script_build", false, 9803625084105160024], [5008152033733129214, "build_script_build", false, 5846861471329538880]], "local": [{"RerunIfChanged": {"output": "release\\build\\jdnotes-ad6599d5356b09d4\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}