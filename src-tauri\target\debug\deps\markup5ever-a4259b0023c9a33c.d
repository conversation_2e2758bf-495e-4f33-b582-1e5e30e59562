E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\deps\markup5ever-a4259b0023c9a33c.d: D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/generated.rs E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/named_entities.rs

E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\deps\libmarkup5ever-a4259b0023c9a33c.rlib: D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/generated.rs E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/named_entities.rs

E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\deps\libmarkup5ever-a4259b0023c9a33c.rmeta: D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/generated.rs E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/named_entities.rs

D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs:
D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs:
D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs:
D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs:
D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs:
D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs:
D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs:
E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/generated.rs:
E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\markup5ever-506fc92b0899210e\out/named_entities.rs:

# env-dep:OUT_DIR=E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\markup5ever-506fc92b0899210e\\out
