{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17690913698614214024, "build_script_build", false, 14599719557502345825], [12092653563678505622, "build_script_build", false, 15505707018007802362], [8324462083842905811, "build_script_build", false, 827947225173266529], [5008152033733129214, "build_script_build", false, 9224474293165040544]], "local": [{"RerunIfChanged": {"output": "debug\\build\\jdnotes-7df7786a146ece61\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}