{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 10389962791146233759, "deps": [[654232091421095663, "tauri_utils", false, 17816353002850615878], [3150220818285335163, "url", false, 5391303607643370604], [4143744114649553716, "raw_window_handle", false, 8871094038573090994], [7606335748176206944, "dpi", false, 10652289890363185665], [8569119365930580996, "serde_json", false, 16260449719180404851], [9010263965687315507, "http", false, 4609389182342060280], [9689903380558560274, "serde", false, 2747036541906619410], [10806645703491011684, "thiserror", false, 4162130665435751246], [12943761728066819757, "build_script_build", false, 1904861539538371001], [14585479307175734061, "windows", false, 16572681052828898213], [16727543399706004146, "cookie", false, 3831476817164181169]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-fe1daea75bff0a5f\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}