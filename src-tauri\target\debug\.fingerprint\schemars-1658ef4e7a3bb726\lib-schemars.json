{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 14627915132391153963, "deps": [[3150220818285335163, "url", false, 2227905525283752008], [6913375703034175521, "build_script_build", false, 1062029339344368048], [8319709847752024821, "uuid1", false, 11231039113021908532], [8569119365930580996, "serde_json", false, 9801121442321344201], [9122563107207267705, "dyn_clone", false, 13336709506101428405], [9689903380558560274, "serde", false, 2747036541906619410], [14923790796823607459, "indexmap", false, 11579635979862800922], [16071897500792579091, "schemars_derive", false, 14006565379778330208]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-1658ef4e7a3bb726\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}