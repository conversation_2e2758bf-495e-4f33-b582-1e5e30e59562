/**
 * 数据库迁移管理
 * 处理数据库版本升级和数据迁移
 */

import type { Migration, DatabaseService } from './database-service'
// 原有的笔记类型定义（用于从 localStorage 迁移）
interface OldNote {
  id: string
  title: string
  content: string
  tags: string[]
  isFavorite: boolean
  createdAt: Date
  updatedAt: Date
}

/**
 * 数据库迁移定义
 */
export const migrations: Migration[] = [
  {
    version: 1,
    description: 'Initial database schema with notes, tags, and full-text search',
    sql: `
      -- 笔记表
      CREATE TABLE IF NOT EXISTS notes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL DEFAULT '无标题',
          content TEXT DEFAULT '',
          is_favorite BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- 标签表
      CREATE TABLE IF NOT EXISTS tags (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT UNIQUE NOT NULL,
          color TEXT DEFAULT '#6B7280',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- 笔记标签关联表
      CREATE TABLE IF NOT EXISTS note_tags (
          note_id INTEGER NOT NULL,
          tag_id INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (note_id, tag_id),
          FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
          FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
      );

      -- 创建索引
      CREATE INDEX IF NOT EXISTS idx_notes_updated_at ON notes(updated_at DESC);
      CREATE INDEX IF NOT EXISTS idx_notes_created_at ON notes(created_at DESC);
      CREATE INDEX IF NOT EXISTS idx_notes_is_favorite ON notes(is_favorite);
      CREATE INDEX IF NOT EXISTS idx_notes_title ON notes(title);
      CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
      CREATE INDEX IF NOT EXISTS idx_note_tags_note_id ON note_tags(note_id);
      CREATE INDEX IF NOT EXISTS idx_note_tags_tag_id ON note_tags(tag_id);

      -- 全文搜索虚拟表
      CREATE VIRTUAL TABLE IF NOT EXISTS notes_fts USING fts5(
          title, 
          content, 
          content='notes', 
          content_rowid='id',
          tokenize='unicode61 remove_diacritics 1'
      );

      -- FTS 同步触发器
      CREATE TRIGGER IF NOT EXISTS notes_fts_insert AFTER INSERT ON notes BEGIN
          INSERT INTO notes_fts(rowid, title, content) VALUES (new.id, new.title, new.content);
      END;

      CREATE TRIGGER IF NOT EXISTS notes_fts_update AFTER UPDATE ON notes BEGIN
          UPDATE notes_fts SET title = new.title, content = new.content WHERE rowid = new.id;
      END;

      CREATE TRIGGER IF NOT EXISTS notes_fts_delete AFTER DELETE ON notes BEGIN
          DELETE FROM notes_fts WHERE rowid = old.id;
      END;

      -- 自动更新时间戳触发器
      CREATE TRIGGER IF NOT EXISTS notes_updated_at AFTER UPDATE ON notes BEGIN
          UPDATE notes SET updated_at = CURRENT_TIMESTAMP WHERE id = new.id;
      END;

      -- 插入默认标签
      INSERT OR IGNORE INTO tags (name, color) VALUES 
          ('工作', '#EF4444'),
          ('学习', '#3B82F6'),
          ('生活', '#10B981'),
          ('重要', '#F59E0B'),
          ('想法', '#8B5CF6'),
          ('代码', '#6366F1'),
          ('笔记', '#6B7280');

      -- 数据库版本表
      CREATE TABLE IF NOT EXISTS db_version (
          version INTEGER PRIMARY KEY,
          applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          description TEXT
      );
    `
  }
  // 未来的迁移可以在这里添加
  // {
  //   version: 2,
  //   description: 'Add note categories',
  //   sql: `
  //     ALTER TABLE notes ADD COLUMN category_id INTEGER;
  //     CREATE TABLE categories (...);
  //   `
  // }
]

/**
 * 数据库迁移管理器
 */
export class MigrationManager {
  constructor(private db: DatabaseService) {}

  /**
   * 执行所有待执行的迁移
   */
  async migrate(): Promise<void> {
    const currentVersion = await this.getCurrentVersion()
    const pendingMigrations = migrations.filter(m => m.version > currentVersion)

    if (pendingMigrations.length === 0) {
      console.log('Database is up to date')
      return
    }

    console.log(`Applying ${pendingMigrations.length} migrations...`)

    for (const migration of pendingMigrations) {
      await this.applyMigration(migration)
    }

    console.log('All migrations applied successfully')
  }

  /**
   * 应用单个迁移
   */
  private async applyMigration(migration: Migration): Promise<void> {
    try {
      console.log(`Applying migration ${migration.version}: ${migration.description}`)
      
      // 执行迁移SQL
      await this.executeSql(migration.sql)
      
      // 记录迁移历史
      await this.recordMigration(migration)
      
      console.log(`Migration ${migration.version} applied successfully`)
    } catch (error) {
      console.error(`Failed to apply migration ${migration.version}:`, error)
      throw error
    }
  }

  /**
   * 获取当前数据库版本
   */
  private async getCurrentVersion(): Promise<number> {
    try {
      return await this.db.getCurrentVersion()
    } catch {
      // 如果版本表不存在，说明是全新数据库
      return 0
    }
  }

  /**
   * 记录迁移历史
   */
  private async recordMigration(migration: Migration): Promise<void> {
    const sql = `
      INSERT INTO db_version (version, description, applied_at) 
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `
    await this.executeSql(sql, [migration.version, migration.description])
  }

  /**
   * 执行SQL语句（这个方法需要在具体的数据库服务中实现）
   */
  private async executeSql(sql: string, params?: any[]): Promise<any> {
    // 这里需要调用具体数据库服务的执行方法
    // 由于接口中没有定义通用的SQL执行方法，这里先抛出错误
    // 在具体实现中需要重写这个方法
    throw new Error('executeSql method must be implemented in concrete database service')
  }
}

/**
 * 从 localStorage 迁移数据到 SQLite
 */
export class LocalStorageMigrator {
  constructor(private db: DatabaseService) {}

  /**
   * 检查是否需要从 localStorage 迁移数据
   */
  async needsMigration(): Promise<boolean> {
    // 检查 localStorage 中是否有旧数据
    if (typeof window === 'undefined') return false
    
    const oldNotes = localStorage.getItem('notes')
    if (!oldNotes) return false

    // 检查数据库中是否已有数据
    const stats = await this.db.getStats()
    return stats.totalNotes === 0 && oldNotes
  }

  /**
   * 执行从 localStorage 到 SQLite 的数据迁移
   */
  async migrate(): Promise<void> {
    if (typeof window === 'undefined') {
      console.log('Not in browser environment, skipping localStorage migration')
      return
    }

    try {
      console.log('Starting localStorage to SQLite migration...')

      // 获取旧数据
      const oldNotesData = localStorage.getItem('notes')
      const oldSelectedTag = localStorage.getItem('selectedTag')
      const oldSortBy = localStorage.getItem('sortBy')

      if (!oldNotesData) {
        console.log('No notes found in localStorage')
        return
      }

      // 解析旧笔记数据
      const oldNotes: OldNote[] = JSON.parse(oldNotesData).map((note: any) => ({
        ...note,
        isFavorite: note.isFavorite || false,
        tags: note.tags || [],
        createdAt: new Date(note.createdAt),
        updatedAt: new Date(note.updatedAt),
      }))

      console.log(`Found ${oldNotes.length} notes to migrate`)

      // 收集所有标签
      const allTags = new Set<string>()
      oldNotes.forEach(note => {
        note.tags.forEach(tag => allTags.add(tag))
      })

      // 创建标签映射
      const tagMap = new Map<string, number>()
      for (const tagName of allTags) {
        try {
          const tag = await this.db.createTag({ name: tagName })
          tagMap.set(tagName, tag.id)
        } catch (error) {
          // 标签可能已存在，尝试获取
          const existingTag = await this.db.getTagByName(tagName)
          if (existingTag) {
            tagMap.set(tagName, existingTag.id)
          }
        }
      }

      // 迁移笔记
      let migratedCount = 0
      for (const oldNote of oldNotes) {
        try {
          // 创建笔记
          const newNote = await this.db.createNote({
            title: oldNote.title,
            content: oldNote.content,
            isFavorite: oldNote.isFavorite
          })

          // 添加标签关联
          const tagIds = oldNote.tags
            .map(tagName => tagMap.get(tagName))
            .filter((id): id is number => id !== undefined)

          if (tagIds.length > 0) {
            await this.db.setNoteTags(newNote.id, tagIds)
          }

          migratedCount++
        } catch (error) {
          console.error('Failed to migrate note:', oldNote.title, error)
        }
      }

      console.log(`Successfully migrated ${migratedCount} notes`)

      // 备份旧数据并清理
      this.backupAndCleanLocalStorage()

    } catch (error) {
      console.error('Failed to migrate from localStorage:', error)
      throw error
    }
  }

  /**
   * 备份并清理 localStorage 数据
   */
  private backupAndCleanLocalStorage(): void {
    try {
      const backup = {
        notes: localStorage.getItem('notes'),
        selectedTag: localStorage.getItem('selectedTag'),
        sortBy: localStorage.getItem('sortBy'),
        migratedAt: new Date().toISOString()
      }

      // 保存备份
      localStorage.setItem('notes_backup', JSON.stringify(backup))

      // 清理旧数据
      localStorage.removeItem('notes')
      
      // 保留用户偏好设置
      // localStorage.removeItem('selectedTag')
      // localStorage.removeItem('sortBy')

      console.log('localStorage data backed up and cleaned')
    } catch (error) {
      console.error('Failed to backup localStorage data:', error)
    }
  }
}
