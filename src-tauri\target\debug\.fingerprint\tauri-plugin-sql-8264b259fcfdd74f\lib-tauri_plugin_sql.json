{"rustc": 1842507548689473721, "features": "[\"sqlite\"]", "declared_features": "[\"mysql\", \"postgres\", \"sqlite\"]", "target": 2554770350637848829, "profile": 15657897354478470176, "path": 6259542185278175390, "deps": [[5008152033733129214, "build_script_build", false, 9224474293165040544], [5986029879202738730, "log", false, 13360595710959020976], [6493259146304816786, "indexmap", false, 13226878895902020250], [6841140121864026414, "sqlx", false, 16830567366612066498], [7620660491849607393, "futures_core", false, 416163346381023690], [8569119365930580996, "serde_json", false, 16260449719180404851], [9689903380558560274, "serde", false, 2747036541906619410], [10806645703491011684, "thiserror", false, 4162130665435751246], [12092653563678505622, "tauri", false, 1670000709277920992], [12393800526703971956, "tokio", false, 3583243449962576455], [12409575957772518135, "time", false, 5078045703711303340]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-sql-8264b259fcfdd74f\\dep-lib-tauri_plugin_sql", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}