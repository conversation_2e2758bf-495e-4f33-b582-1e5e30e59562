"use client"

import React, { useEffect, useState } from "react"
import { useTauri } from "@/hooks/use-tauri"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function TauriDebug() {
  const { isTauri, isLoading } = useTauri()
  const [windowInfo, setWindowInfo] = useState<any>(null)

  useEffect(() => {
    const getWindowInfo = async () => {
      if (!isTauri) return

      try {
        const { getCurrentWindow } = await import("@tauri-apps/api/window")
        const currentWindow = getCurrentWindow()
        
        const [isMaximized, isMinimized, isVisible, isResizable] = await Promise.all([
          currentWindow.isMaximized(),
          currentWindow.isMinimized(),
          currentWindow.isVisible(),
          currentWindow.isResizable(),
        ])

        setWindowInfo({
          isMaximized,
          isMinimized,
          isVisible,
          isResizable,
        })
      } catch (error) {
        console.error("Failed to get window info:", error)
      }
    }

    getWindowInfo()
  }, [isTauri])

  if (isLoading) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Tauri 状态</CardTitle>
        </CardHeader>
        <CardContent>
          <p>检测中...</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Tauri 状态</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="flex items-center gap-2">
          <span>环境:</span>
          <Badge variant={isTauri ? "default" : "secondary"}>
            {isTauri ? "Tauri 桌面应用" : "Web 浏览器"}
          </Badge>
        </div>
        
        {isTauri && windowInfo && (
          <div className="space-y-1 text-sm">
            <div className="flex items-center gap-2">
              <span>最大化:</span>
              <Badge variant={windowInfo.isMaximized ? "default" : "outline"}>
                {windowInfo.isMaximized ? "是" : "否"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span>最小化:</span>
              <Badge variant={windowInfo.isMinimized ? "default" : "outline"}>
                {windowInfo.isMinimized ? "是" : "否"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span>可见:</span>
              <Badge variant={windowInfo.isVisible ? "default" : "outline"}>
                {windowInfo.isVisible ? "是" : "否"}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span>可调整大小:</span>
              <Badge variant={windowInfo.isResizable ? "default" : "outline"}>
                {windowInfo.isResizable ? "是" : "否"}
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
