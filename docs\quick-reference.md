# Tauri 2 自定义标题栏快速参考

## 🚀 快速解决标题栏不显示问题

### 1. 修复 Rust 代码（必须）
```rust
// src-tauri/src/lib.rs
use tauri::Manager;  // ← 添加这行！

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .setup(|app| {
      let window = app.get_webview_window("main").unwrap();
      // 窗口事件监听...
      Ok(())
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
```

### 2. 配置权限（必须）
```json
// src-tauri/capabilities/default.json
{
  "permissions": [
    "core:default",
    "core:window:allow-close",
    "core:window:allow-minimize",
    "core:window:allow-maximize",
    "core:window:allow-unmaximize", 
    "core:window:allow-toggle-maximize",
    "core:window:allow-start-dragging"
  ]
}
```

### 3. Tauri 配置（必须）
```json
// src-tauri/tauri.conf.json
{
  "app": {
    "windows": [
      {
        "decorations": false,  // 禁用系统标题栏
        "transparent": true    // 启用透明背景
      }
    ]
  }
}
```

### 4. 环境检测 Hook（推荐）
```typescript
// hooks/use-tauri.ts
export function useTauri() {
  const [isTauri, setIsTauri] = useState(false)

  useEffect(() => {
    const checkTauri = () => {
      if (typeof window !== "undefined") {
        const hasTauri = window.__TAURI__ !== undefined || 
                        window.__TAURI_INTERNALS__ !== undefined
        setIsTauri(hasTauri)
      }
    }
    
    const timer = setTimeout(checkTauri, 100)
    return () => clearTimeout(timer)
  }, [])

  return { isTauri }
}
```

### 5. 简单标题栏组件（推荐）
```typescript
// components/simple-titlebar.tsx
export function SimpleTitlebar() {
  const { isTauri } = useTauri()

  const handleMinimize = async () => {
    try {
      const { getCurrentWindow } = await import("@tauri-apps/api/window")
      await getCurrentWindow().minimize()
    } catch (error) {
      console.error("Failed to minimize:", error)
    }
  }

  if (!isTauri) return null

  return (
    <div className="flex items-center justify-between h-10 bg-gray-50 border-b">
      <div 
        className="flex-1 flex items-center px-4 cursor-move"
        onMouseDown={async () => {
          const { getCurrentWindow } = await import("@tauri-apps/api/window")
          await getCurrentWindow().startDragging()
        }}
      >
        <span>我的应用</span>
      </div>
      
      <div className="flex">
        <button onClick={handleMinimize}>−</button>
        <button onClick={async () => {
          const { getCurrentWindow } = await import("@tauri-apps/api/window")
          await getCurrentWindow().toggleMaximize()
        }}>□</button>
        <button onClick={async () => {
          const { getCurrentWindow } = await import("@tauri-apps/api/window")
          await getCurrentWindow().close()
        }}>×</button>
      </div>
    </div>
  )
}
```

## 🔧 常见错误及解决

### 错误 1: `no method named 'get_webview_window'`
**解决：** 添加 `use tauri::Manager;`

### 错误 2: 标题栏不显示
**解决：** 检查环境检测逻辑，确保 `isTauri` 为 true

### 错误 3: 按钮无响应
**解决：** 检查权限配置，确保包含所有窗口控制权限

### 错误 4: 无法拖拽
**解决：** 添加 CSS 属性 `-webkit-app-region: drag`

## 📋 检查清单

- [ ] ✅ 添加了 `use tauri::Manager;`
- [ ] ✅ 配置了窗口控制权限
- [ ] ✅ 设置了 `decorations: false`
- [ ] ✅ 实现了环境检测
- [ ] ✅ 添加了错误处理
- [ ] ✅ 测试了所有窗口控制功能

## 🚨 故障排除

### 开发环境测试
```bash
npm run tauri dev
```

### 生产构建测试
```bash
npm run build
npm run tauri build
```

### 调试技巧
```typescript
// 添加调试信息
console.log('Tauri detected:', window.__TAURI__ !== undefined)
console.log('Environment:', process.env.NODE_ENV)
```

## 📚 相关文档

- [完整实现指南](./custom-titlebar-implementation.md)
- [Tauri 2 官方文档](https://tauri.app/v1/guides/)
- [窗口 API 参考](https://tauri.app/v1/api/js/window)

---

💡 **提示：** 如果遇到问题，首先检查浏览器控制台的错误信息，然后按照检查清单逐项验证配置。
