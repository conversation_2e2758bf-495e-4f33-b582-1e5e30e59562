/**
 * 数据库服务使用示例
 * 展示如何在应用中使用数据库服务
 */

import { 
  getDatabase, 
  initializeDatabase,
  type Note,
  type Tag,
  type CreateNoteInput,
  type NoteQueryParams 
} from './index'

/**
 * 应用启动时的数据库初始化示例
 */
export async function initApp() {
  try {
    // 初始化数据库，包含进度回调
    await initializeDatabase({
      enableMigration: true,
      enableLocalStorageMigration: true,
      onProgress: (step, progress) => {
        console.log(`${step} (${progress}%)`)
        // 可以在这里更新 UI 进度条
      }
    })

    console.log('App initialized successfully')
  } catch (error) {
    console.error('Failed to initialize app:', error)
    // 处理初始化失败的情况
  }
}

/**
 * 笔记操作示例
 */
export class NotesManager {
  private db = getDatabase()

  /**
   * 创建新笔记
   */
  async createNote(title: string, content: string, tagNames: string[] = []): Promise<Note> {
    const db = await this.db

    // 创建笔记
    const note = await db.createNote({
      title,
      content,
      isFavorite: false
    })

    // 处理标签
    if (tagNames.length > 0) {
      const tagIds = await this.getOrCreateTags(tagNames)
      await db.setNoteTags(note.id, tagIds)
    }

    return note
  }

  /**
   * 更新笔记
   */
  async updateNote(id: number, updates: Partial<CreateNoteInput>): Promise<Note> {
    const db = await this.db
    return await db.updateNote(id, updates)
  }

  /**
   * 删除笔记
   */
  async deleteNote(id: number): Promise<void> {
    const db = await this.db
    await db.deleteNote(id)
  }

  /**
   * 搜索笔记
   */
  async searchNotes(searchTerm: string, options: {
    tagIds?: number[]
    isFavorite?: boolean
    limit?: number
  } = {}): Promise<Note[]> {
    const db = await this.db

    if (searchTerm.trim()) {
      // 使用全文搜索
      const searchResults = await db.searchNotes(searchTerm, {
        tagIds: options.tagIds,
        isFavorite: options.isFavorite,
        limit: options.limit
      })
      return searchResults.map(result => result.note)
    } else {
      // 普通查询
      const result = await db.queryNotes({
        tagIds: options.tagIds,
        isFavorite: options.isFavorite,
        limit: options.limit,
        sortBy: 'updated',
        sortDirection: 'desc'
      })
      return result.data
    }
  }

  /**
   * 获取收藏笔记
   */
  async getFavoriteNotes(): Promise<Note[]> {
    const db = await this.db
    const result = await db.getFavoriteNotes({
      sortBy: 'updated',
      sortDirection: 'desc'
    })
    return result.data
  }

  /**
   * 切换收藏状态
   */
  async toggleFavorite(id: number): Promise<Note> {
    const db = await this.db
    return await db.toggleNoteFavorite(id)
  }

  /**
   * 获取或创建标签
   */
  private async getOrCreateTags(tagNames: string[]): Promise<number[]> {
    const db = await this.db
    const tagIds: number[] = []

    for (const name of tagNames) {
      let tag = await db.getTagByName(name)
      if (!tag) {
        tag = await db.createTag({ name })
      }
      tagIds.push(tag.id)
    }

    return tagIds
  }
}

/**
 * 标签管理示例
 */
export class TagsManager {
  private db = getDatabase()

  /**
   * 获取所有标签及其使用统计
   */
  async getAllTagsWithStats(): Promise<Tag[]> {
    const db = await this.db
    return await db.getTagStats()
  }

  /**
   * 创建新标签
   */
  async createTag(name: string, color?: string): Promise<Tag> {
    const db = await this.db
    return await db.createTag({ name, color })
  }

  /**
   * 更新标签
   */
  async updateTag(id: number, name?: string, color?: string): Promise<Tag> {
    const db = await this.db
    return await db.updateTag(id, { name, color })
  }

  /**
   * 删除标签
   */
  async deleteTag(id: number): Promise<void> {
    const db = await this.db
    await db.deleteTag(id)
  }

  /**
   * 获取使用指定标签的笔记
   */
  async getNotesByTag(tagId: number): Promise<Note[]> {
    const db = await this.db
    const result = await db.getNotesByTagId(tagId, {
      sortBy: 'updated',
      sortDirection: 'desc'
    })
    return result.data
  }
}

/**
 * 数据导入导出示例
 */
export class DataManager {
  private db = getDatabase()

  /**
   * 导出所有数据
   */
  async exportAllData(): Promise<string> {
    const db = await this.db
    const exportData = await db.exportData()
    
    // 转换为 JSON 字符串
    return JSON.stringify(exportData, null, 2)
  }

  /**
   * 从 JSON 字符串导入数据
   */
  async importFromJson(jsonData: string, overwrite = false): Promise<void> {
    const db = await this.db
    
    try {
      const data = JSON.parse(jsonData)
      await db.importData(data, {
        overwriteExisting: overwrite,
        mergeMode: overwrite ? 'replace' : 'merge',
        validateData: true
      })
    } catch (error) {
      throw new Error(`Invalid JSON data: ${error}`)
    }
  }

  /**
   * 创建备份
   */
  async createBackup(): Promise<string> {
    const db = await this.db
    return await db.backup()
  }

  /**
   * 从备份恢复
   */
  async restoreFromBackup(backupData: string): Promise<void> {
    const db = await this.db
    await db.restore(backupData)
  }
}

/**
 * React Hook 示例（用于在 React 组件中使用）
 */
export function useDatabase() {
  const notesManager = new NotesManager()
  const tagsManager = new TagsManager()
  const dataManager = new DataManager()

  return {
    // 笔记操作
    createNote: notesManager.createNote.bind(notesManager),
    updateNote: notesManager.updateNote.bind(notesManager),
    deleteNote: notesManager.deleteNote.bind(notesManager),
    searchNotes: notesManager.searchNotes.bind(notesManager),
    getFavoriteNotes: notesManager.getFavoriteNotes.bind(notesManager),
    toggleFavorite: notesManager.toggleFavorite.bind(notesManager),

    // 标签操作
    getAllTagsWithStats: tagsManager.getAllTagsWithStats.bind(tagsManager),
    createTag: tagsManager.createTag.bind(tagsManager),
    updateTag: tagsManager.updateTag.bind(tagsManager),
    deleteTag: tagsManager.deleteTag.bind(tagsManager),
    getNotesByTag: tagsManager.getNotesByTag.bind(tagsManager),

    // 数据管理
    exportAllData: dataManager.exportAllData.bind(dataManager),
    importFromJson: dataManager.importFromJson.bind(dataManager),
    createBackup: dataManager.createBackup.bind(dataManager),
    restoreFromBackup: dataManager.restoreFromBackup.bind(dataManager)
  }
}
