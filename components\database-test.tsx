"use client"

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

// 数据库测试组件
export function DatabaseTest() {
  const [testResults, setTestResults] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [dbInfo, setDbInfo] = useState<any>(null)

  // 添加测试结果
  const addResult = (message: string, isError = false) => {
    const timestamp = new Date().toLocaleTimeString()
    const formattedMessage = `[${timestamp}] ${isError ? '❌' : '✅'} ${message}`
    setTestResults(prev => [...prev, formattedMessage])
  }

  // 检查运行环境
  const checkEnvironment = () => {
    addResult('开始环境检查...')
    
    // 检查是否在 Tauri 环境中
    if (typeof window !== 'undefined') {
      const isTauri = !!(window as any).__TAURI__
      const hasAPI = !!(window as any).__TAURI_INTERNALS__
      
      addResult(`Tauri 环境: ${isTauri ? '是' : '否'}`)
      addResult(`Tauri API: ${hasAPI ? '可用' : '不可用'}`)
      
      if (isTauri) {
        addResult('✨ 检测到 Tauri 环境，可以使用 SQLite')
      } else {
        addResult('ℹ️ 当前在浏览器环境中运行')
        addResult('💡 要测试 SQLite，请使用 `npm run tauri dev` 启动桌面应用')
        addResult('🌐 Web 环境将使用备用数据库实现（开发中）')
      }
    }
  }

  // 测试数据库连接
  const testDatabaseConnection = async () => {
    try {
      addResult('正在测试数据库连接...')

      // 检查环境
      const isTauri = typeof window !== 'undefined' && !!(window as any).__TAURI__
      if (!isTauri) {
        addResult('⚠️ 跳过数据库测试：需要在 Tauri 桌面应用中运行')
        addResult('📖 使用说明：运行 `npm run tauri dev` 然后在桌面应用中测试')
        return null
      }

      // 动态导入数据库模块
      const { initializeDatabase, getDatabase } = await import('@/lib/database')
      
      // 初始化数据库
      await initializeDatabase({
        config: {
          name: 'test-verification.db',
          version: 1
        },
        onProgress: (step, progress) => {
          addResult(`初始化进度: ${step} (${progress}%)`)
        }
      })
      
      // 获取数据库实例
      const db = await getDatabase()
      addResult(`数据库平台: ${db.getPlatform()}`)
      addResult(`数据库状态: ${db.getStatus()}`)
      
      // 获取数据库统计
      const stats = await db.getStats()
      setDbInfo(stats)
      addResult(`数据库统计获取成功`)
      
      return db
    } catch (error) {
      addResult(`数据库连接失败: ${error}`, true)
      throw error
    }
  }

  // 测试基本 CRUD 操作
  const testCRUDOperations = async (db: any) => {
    if (!db) {
      addResult('⏭️ 跳过 CRUD 测试：数据库未连接')
      return
    }

    try {
      addResult('开始测试 CRUD 操作...')
      
      // 创建标签
      const tag = await db.createTag({
        name: '测试标签',
        color: '#FF6B6B'
      })
      addResult(`创建标签成功: ${tag.name} (ID: ${tag.id})`)
      
      // 创建笔记
      const note = await db.createNote({
        title: '测试笔记',
        content: '这是一个测试笔记的内容，用于验证 SQLite 数据库功能。',
        isFavorite: true,
        tagIds: [tag.id]
      })
      addResult(`创建笔记成功: ${note.title} (ID: ${note.id})`)
      
      // 查询笔记
      const notes = await db.queryNotes({ limit: 10 })
      addResult(`查询笔记成功: 找到 ${notes.total} 条笔记`)
      
      // 更新笔记
      const updatedNote = await db.updateNote(note.id, {
        title: '已更新的测试笔记',
        content: note.content + '\n\n这是更新后添加的内容。'
      })
      addResult(`更新笔记成功: ${updatedNote.title}`)
      
      // 搜索测试
      const searchResults = await db.searchNotes('测试')
      addResult(`搜索功能测试: 找到 ${searchResults.length} 条结果`)
      
      // 删除测试数据
      await db.deleteNote(note.id)
      await db.deleteTag(tag.id)
      addResult('清理测试数据完成')
      
    } catch (error) {
      addResult(`CRUD 操作失败: ${error}`, true)
      throw error
    }
  }

  // 运行完整测试
  const runFullTest = async () => {
    setIsLoading(true)
    setTestResults([])
    setDbInfo(null)
    
    try {
      // 环境检查
      checkEnvironment()
      
      // 数据库连接测试
      const db = await testDatabaseConnection()
      
      // CRUD 操作测试
      await testCRUDOperations(db)
      
      addResult('🎉 所有测试通过！SQLite 数据库工作正常')
      
    } catch (error) {
      addResult(`测试失败: ${error}`, true)
    } finally {
      setIsLoading(false)
    }
  }

  // 清除测试结果
  const clearResults = () => {
    setTestResults([])
    setDbInfo(null)
  }

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      {/* 环境说明卡片 */}
      {typeof window !== 'undefined' && !(window as any).__TAURI__ && (
        <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
              ℹ️ 环境说明
            </CardTitle>
          </CardHeader>
          <CardContent className="text-blue-600 dark:text-blue-400 space-y-2">
            <p>当前在浏览器环境中运行，无法测试 SQLite 数据库功能。</p>
            <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-lg">
              <p className="font-semibold mb-2">要测试 SQLite 数据库，请：</p>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>在终端运行：<code className="bg-blue-200 dark:bg-blue-800 px-1 rounded">npm run tauri dev</code></li>
                <li>等待桌面应用启动</li>
                <li>在桌面应用中点击侧边栏的 "测试 SQLite 数据库" 按钮</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🗄️ SQLite 数据库验证工具
            <Badge variant="outline">Tauri + SQLite</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button 
              onClick={runFullTest} 
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? '测试中...' : '开始测试'}
            </Button>
            <Button 
              onClick={clearResults} 
              variant="outline"
              disabled={isLoading}
            >
              清除结果
            </Button>
          </div>
          
          {dbInfo && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{dbInfo.totalNotes}</div>
                <div className="text-sm text-gray-600">总笔记数</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{dbInfo.totalTags}</div>
                <div className="text-sm text-gray-600">总标签数</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{dbInfo.favoriteNotes}</div>
                <div className="text-sm text-gray-600">收藏笔记</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{dbInfo.untaggedNotes}</div>
                <div className="text-sm text-gray-600">无标签笔记</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>测试结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className="mb-1">
                  {result}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>验证说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-gray-600">
          <p>• <strong>环境检查</strong>: 验证是否在 Tauri 环境中运行</p>
          <p>• <strong>数据库连接</strong>: 测试 SQLite 数据库的初始化和连接</p>
          <p>• <strong>CRUD 操作</strong>: 测试创建、读取、更新、删除功能</p>
          <p>• <strong>搜索功能</strong>: 验证全文搜索是否正常工作</p>
          <p>• <strong>数据统计</strong>: 检查数据库统计信息获取</p>
        </CardContent>
      </Card>
    </div>
  )
}
