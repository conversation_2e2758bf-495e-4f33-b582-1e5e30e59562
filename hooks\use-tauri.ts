"use client"

import { useState, useEffect } from "react"

export function useTauri() {
  const [isTauri, setIsTauri] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkTauri = async () => {
      try {
        // 检查是否在 Tauri 环境中
        if (typeof window !== "undefined") {
          // 多种方式检测 Tauri 环境
          // @ts-ignore
          const hasTauriGlobal = window.__TAURI__ !== undefined
          // @ts-ignore
          const hasTauriAPI = window.__TAURI_INTERNALS__ !== undefined

          // 尝试导入 Tauri API 来进一步确认
          let hasTauriImport = false
          try {
            await import("@tauri-apps/api/core")
            hasTauriImport = true
          } catch {
            hasTauriImport = false
          }

          const isInTauri = hasTauriGlobal || hasTauriAPI || hasTauriImport
          setIsTauri(isInTauri)

          if (isInTauri) {
            console.log("✅ Tauri environment detected")
          } else {
            console.log("🌐 Web browser environment detected")
          }
        }
      } catch (error) {
        console.log("Not in Tauri environment:", error)
        setIsTauri(false)
      } finally {
        setIsLoading(false)
      }
    }

    // 延迟检测，确保 Tauri 完全初始化
    const timer = setTimeout(checkTauri, 100)

    return () => clearTimeout(timer)
  }, [])

  return { isTauri, isLoading }
}
