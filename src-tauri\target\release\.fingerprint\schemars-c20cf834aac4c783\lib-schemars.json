{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 1369601567987815722, "path": 14627915132391153963, "deps": [[3150220818285335163, "url", false, 4920937753471628383], [6913375703034175521, "build_script_build", false, 10537191381389386304], [8319709847752024821, "uuid1", false, 1810398405112657693], [8569119365930580996, "serde_json", false, 15384600167726692329], [9122563107207267705, "dyn_clone", false, 5513239738382897684], [9689903380558560274, "serde", false, 9456690565758718592], [14923790796823607459, "indexmap", false, 17332400171065101501], [16071897500792579091, "schemars_derive", false, 16610587776479346144]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\schemars-c20cf834aac4c783\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}