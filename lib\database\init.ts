/**
 * 数据库初始化模块
 * 处理数据库的初始化、迁移和数据导入
 */

import { getDatabase } from './database-service'
import { MigrationManager, LocalStorageMigrator } from './migrations'
import type { DatabaseConfig } from './types'

/**
 * 数据库初始化选项
 */
export interface InitOptions {
  config?: DatabaseConfig
  enableMigration?: boolean
  enableLocalStorageMigration?: boolean
  onProgress?: (step: string, progress: number) => void
}

/**
 * 初始化数据库
 * 这是应用启动时应该调用的主要函数
 */
export async function initializeDatabase(options: InitOptions = {}): Promise<void> {
  const {
    config,
    enableMigration = true,
    enableLocalStorageMigration = true,
    onProgress
  } = options

  try {
    onProgress?.('正在连接数据库...', 0)

    // 1. 获取数据库服务实例
    const db = await getDatabase()
    
    onProgress?.('正在初始化数据库...', 20)

    // 2. 初始化数据库连接
    await db.initialize(config)

    onProgress?.('正在检查数据库版本...', 40)

    // 3. 执行数据库迁移
    if (enableMigration) {
      const migrationManager = new MigrationManager(db)
      await migrationManager.migrate()
    }

    onProgress?.('正在检查数据迁移...', 60)

    // 4. 从 localStorage 迁移数据（如果需要）
    if (enableLocalStorageMigration) {
      const localStorageMigrator = new LocalStorageMigrator(db)
      
      if (await localStorageMigrator.needsMigration()) {
        onProgress?.('正在迁移本地数据...', 80)
        await localStorageMigrator.migrate()
      }
    }

    onProgress?.('数据库初始化完成', 100)

    console.log('Database initialized successfully')
    console.log(`Platform: ${db.getPlatform()}`)
    console.log(`Status: ${db.getStatus()}`)

    // 输出数据库统计信息
    const stats = await db.getStats()
    console.log('Database stats:', stats)

  } catch (error) {
    console.error('Failed to initialize database:', error)
    throw error
  }
}

/**
 * 重置数据库
 * 清除所有数据并重新初始化（主要用于开发和测试）
 */
export async function resetDatabase(options: InitOptions = {}): Promise<void> {
  try {
    console.log('Resetting database...')

    const db = await getDatabase()
    
    // 清理数据库
    await db.clearDatabase()
    
    // 重新初始化
    await initializeDatabase({
      ...options,
      enableLocalStorageMigration: false // 重置时不从 localStorage 迁移
    })

    console.log('Database reset successfully')
  } catch (error) {
    console.error('Failed to reset database:', error)
    throw error
  }
}

/**
 * 检查数据库健康状态
 */
export async function checkDatabaseHealth(): Promise<{
  isHealthy: boolean
  issues: string[]
  stats: any
}> {
  const issues: string[] = []
  let stats: any = null

  try {
    const db = await getDatabase()

    // 检查连接状态
    if (db.getStatus() !== 'connected') {
      issues.push('Database is not connected')
    }

    // 检查是否可用
    if (!(await db.isAvailable())) {
      issues.push('Database is not available')
    }

    // 获取统计信息
    try {
      stats = await db.getStats()
    } catch (error) {
      issues.push(`Failed to get database stats: ${error}`)
    }

    // 检查数据完整性
    try {
      const notesCount = await db.getNotesCount()
      const tagsCount = await db.getTagsCount()
      
      if (notesCount < 0 || tagsCount < 0) {
        issues.push('Invalid data counts detected')
      }
    } catch (error) {
      issues.push(`Failed to verify data integrity: ${error}`)
    }

  } catch (error) {
    issues.push(`Database service error: ${error}`)
  }

  return {
    isHealthy: issues.length === 0,
    issues,
    stats
  }
}

/**
 * 优化数据库性能
 */
export async function optimizeDatabase(): Promise<void> {
  try {
    console.log('Optimizing database...')

    const db = await getDatabase()
    await db.optimizeDatabase()

    console.log('Database optimization completed')
  } catch (error) {
    console.error('Failed to optimize database:', error)
    throw error
  }
}

/**
 * 创建数据库备份
 */
export async function createBackup(): Promise<string> {
  try {
    console.log('Creating database backup...')

    const db = await getDatabase()
    const backupData = await db.backup()

    console.log('Database backup created successfully')
    return backupData
  } catch (error) {
    console.error('Failed to create database backup:', error)
    throw error
  }
}

/**
 * 从备份恢复数据库
 */
export async function restoreFromBackup(backupData: string): Promise<void> {
  try {
    console.log('Restoring database from backup...')

    const db = await getDatabase()
    await db.restore(backupData)

    console.log('Database restored successfully')
  } catch (error) {
    console.error('Failed to restore database from backup:', error)
    throw error
  }
}
