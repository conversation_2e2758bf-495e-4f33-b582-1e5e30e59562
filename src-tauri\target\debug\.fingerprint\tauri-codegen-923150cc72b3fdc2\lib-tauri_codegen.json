{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 8879837628796626640, "deps": [[654232091421095663, "tauri_utils", false, 8647791973032122690], [3060637413840920116, "proc_macro2", false, 12829056815806260006], [3150220818285335163, "url", false, 2227905525283752008], [4899080583175475170, "semver", false, 10887131225399735988], [4974441333307933176, "syn", false, 2939883612788580963], [7170110829644101142, "json_patch", false, 17717933050195001128], [7392050791754369441, "ico", false, 10682326940910309452], [8319709847752024821, "uuid", false, 11231039113021908532], [8569119365930580996, "serde_json", false, 9801121442321344201], [9556762810601084293, "brotli", false, 13038189564160369710], [9689903380558560274, "serde", false, 2747036541906619410], [9857275760291862238, "sha2", false, 9071165338192426708], [10806645703491011684, "thiserror", false, 4162130665435751246], [12687914511023397207, "png", false, 13034613678770539607], [13077212702700853852, "base64", false, 16702054904726379632], [15622660310229662834, "walkdir", false, 421446005939063222], [17990358020177143287, "quote", false, 7892140333893364853]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-923150cc72b3fdc2\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}