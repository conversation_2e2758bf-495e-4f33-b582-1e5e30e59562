{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 14809081417461788049, "deps": [[654232091421095663, "tauri_utils", false, 8647791973032122690], [4899080583175475170, "semver", false, 10887131225399735988], [6913375703034175521, "schemars", false, 9027302573918673350], [7170110829644101142, "json_patch", false, 17717933050195001128], [8569119365930580996, "serde_json", false, 9801121442321344201], [9689903380558560274, "serde", false, 2747036541906619410], [12714016054753183456, "tauri_winres", false, 12911739222934146549], [13077543566650298139, "heck", false, 3833667618904260674], [13475171727366188400, "cargo_toml", false, 16341113949998752005], [13625485746686963219, "anyhow", false, 13363454249419097510], [15609422047640926750, "toml", false, 6898844025720440309], [15622660310229662834, "walkdir", false, 421446005939063222], [16928111194414003569, "dirs", false, 1609515387622353247], [17155886227862585100, "glob", false, 14311527598433297175]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-06993b7c27531877\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}