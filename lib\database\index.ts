/**
 * 数据库模块入口文件
 * 导出所有数据库相关的类型、接口和服务
 */

// 导出类型定义
export type {
  Note,
  Tag,
  CreateNoteInput,
  UpdateNoteInput,
  CreateTagInput,
  UpdateTagInput,
  NoteTag,
  SortOption,
  SortDirection,
  NoteQueryParams,
  QueryResult,
  SearchResult,
  DatabaseStatus,
  DatabaseConfig,
  DatabaseStats,
  Migration,
  ExportData,
  ImportOptions,
  ImportResult,
  Platform,
  PlatformDatabase
} from './types'

// 导出错误类型
export { DatabaseError, DatabaseErrorType } from './types'

// 导出服务接口和工厂
export type { DatabaseService } from './database-service'
export { DatabaseServiceFactory, getDatabase } from './database-service'

// 导出迁移相关
export { migrations, MigrationManager, LocalStorageMigrator } from './migrations'

// 导出数据库初始化函数
export { initializeDatabase } from './init'
