{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 9831540453462500854, "deps": [[376837177317575824, "softbuffer", false, 796013645956046037], [654232091421095663, "tauri_utils", false, 17816353002850615878], [2013030631243296465, "webview2_com", false, 14716284576735372477], [3150220818285335163, "url", false, 5391303607643370604], [3722963349756955755, "once_cell", false, 15951163895981159829], [4143744114649553716, "raw_window_handle", false, 8871094038573090994], [5986029879202738730, "log", false, 13360595710959020976], [8826339825490770380, "tao", false, 15297842846384323297], [9010263965687315507, "http", false, 4609389182342060280], [9141053277961803901, "wry", false, 2638701811316932670], [12304025191202589669, "build_script_build", false, 5355976548200665731], [12943761728066819757, "tauri_runtime", false, 2243538292417931881], [14585479307175734061, "windows", false, 16572681052828898213]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-44d1638423dd4795\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}