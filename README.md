# 简单笔记 (JDNotes)

一个基于 Next.js + Tauri 2 的现代化笔记应用，支持代码高亮、标签管理、搜索等功能。

## 特性

### 📝 笔记功能
- 创建、编辑、删除笔记
- 收藏功能
- 标签系统
- 全文搜索（支持标题、内容、标签）
- 代码高亮显示（JavaScript、TypeScript、Python、Java、CSS）
- 自动保存
- 字数统计

### 🎨 界面设计
- 响应式设计（支持移动端和桌面端）
- 深色/浅色主题切换
- 现代化 UI 组件（基于 shadcn/ui）
- **自定义标题栏**（仅桌面应用）

### ⌨️ 快捷键
- `Ctrl+N` - 新建笔记
- `Ctrl+S` - 保存笔记
- `Ctrl+F` - 搜索笔记
- `Esc` - 取消编辑

### 🖥️ 桌面应用特性
- **自定义标题栏** - 与应用设计一体化的标题栏
  - 智能环境检测（只在桌面应用中显示）
  - 窗口控制（最小化、最大化、关闭）
  - 拖拽移动窗口支持
  - 应用图标显示
  - 主题适配（深色/浅色模式）
- 无边框窗口设计
- 透明背景支持
- 统一的滚动条样式
- 响应式布局适配

## 技术栈

- **前端**: Next.js 15 + React 19 + TypeScript
- **桌面应用**: Tauri 2.7.0
- **UI 组件**: Radix UI + shadcn/ui
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **表单**: React Hook Form + Zod

## 开发环境设置

### 前置要求
- Node.js 18+
- Rust 1.77.2+
- pnpm (推荐) 或 npm

### 安装依赖
```bash
# 安装前端依赖
pnpm install

# 安装 Tauri CLI (如果还没有安装)
cargo install tauri-cli --version "^2.0.0"
```

### 开发模式

#### Web 开发
```bash
# 启动 Next.js 开发服务器
pnpm dev
```

#### 桌面应用开发
```bash
# 启动 Tauri 开发模式（会自动启动 Next.js）
pnpm tauri dev
```

### 构建

#### Web 构建
```bash
# 构建 Web 版本
pnpm build
```

#### 桌面应用构建
```bash
# 构建桌面应用
pnpm tauri build
```

## 自定义标题栏

### 特性
- **智能检测**: 只在 Tauri 桌面应用中显示，Web 版本不受影响
- **窗口控制**: 最小化、最大化/还原、关闭按钮
- **拖拽支持**: 点击标题栏区域可拖拽移动窗口
- **状态同步**: 实时显示窗口最大化状态
- **主题适配**: 支持深色/浅色主题

### 实现原理
1. **环境检测**: 使用 `useTauri` hook 检测是否在 Tauri 环境中
2. **权限配置**: 在 `capabilities/default.json` 中配置窗口控制权限
3. **窗口 API**: 使用 `@tauri-apps/api/window` 进行窗口操作
4. **事件监听**: 监听窗口状态变化，实时更新 UI

### 配置文件

#### Tauri 配置 (`src-tauri/tauri.conf.json`)
```json
{
  "app": {
    "windows": [
      {
        "decorations": false,  // 禁用系统标题栏
        "transparent": true    // 启用透明背景
      }
    ]
  }
}
```

#### 权限配置 (`src-tauri/capabilities/default.json`)
```json
{
  "permissions": [
    "core:window:allow-close",
    "core:window:allow-minimize",
    "core:window:allow-maximize",
    "core:window:allow-unmaximize",
    "core:window:allow-toggle-maximize",
    "core:window:allow-start-dragging"
  ]
}
```

### 组件使用
```tsx
import { CustomTitlebar } from "@/components/custom-titlebar"

export default function App() {
  return (
    <div className="flex flex-col h-screen">
      {/* 自定义标题栏 - 只在 Tauri 中显示 */}
      <CustomTitlebar title="我的应用" />
      
      {/* 应用内容 */}
      <div className="flex-1">
        {/* 你的应用内容 */}
      </div>
    </div>
  )
}
```

## 项目结构

```
jdnotes/
├── app/                    # Next.js 应用目录
│   ├── page.tsx           # 主页面
│   ├── layout.tsx         # 布局组件
│   └── globals.css        # 全局样式
├── components/            # React 组件
│   ├── ui/               # shadcn/ui 组件
│   ├── custom-titlebar.tsx # 自定义标题栏
│   └── tauri-debug.tsx   # Tauri 调试组件
├── hooks/                # 自定义 Hooks
│   ├── use-tauri.ts      # Tauri 环境检测
│   └── use-mobile.tsx    # 移动端检测
├── src-tauri/            # Tauri 配置和 Rust 代码
│   ├── src/              # Rust 源码
│   ├── capabilities/     # 权限配置
│   ├── icons/           # 应用图标
│   └── tauri.conf.json  # Tauri 配置
└── public/              # 静态资源
```

## 开发说明

### 调试模式
在开发环境中，侧边栏底部会显示 Tauri 状态信息，包括：
- 运行环境（Web 浏览器 / Tauri 桌面应用）
- 窗口状态（最大化、最小化、可见性等）

### 注意事项
1. 自定义标题栏只在 Tauri 桌面应用中显示
2. Web 版本保持原有的浏览器标题栏
3. 窗口控制按钮的样式会根据主题自动调整
4. 拖拽区域使用 CSS 的 `-webkit-app-region` 属性

## 📖 技术文档

### 自定义标题栏实现
- [完整实现指南](./docs/custom-titlebar-implementation.md) - 详细的问题分析和解决方案
- [快速参考指南](./docs/quick-reference.md) - 快速解决常见问题

### 主要解决的问题
1. **标题栏不显示问题** - 通过正确的 Rust 配置和环境检测解决
2. **滚动条样式统一** - 使用 ScrollArea 组件替换默认滚动条
3. **响应式布局优化** - 编辑区域高度自适应窗口大小
4. **图标集成** - 在标题栏中显示应用图标

### 故障排除
如果遇到标题栏不显示的问题，请参考：
- [故障排除指南](./docs/custom-titlebar-implementation.md#故障排除指南)
- [快速检查清单](./docs/quick-reference.md#检查清单)

## 许可证

MIT License
