-- 笔记应用数据库结构定义
-- 支持 Tauri (桌面) 和 Capacitor (移动端) 双平台

-- 笔记表
CREATE TABLE IF NOT EXISTS notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL DEFAULT '无标题',
    content TEXT DEFAULT '',
    is_favorite BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 标签表
CREATE TABLE IF NOT EXISTS tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    color TEXT DEFAULT '#6B7280', -- 默认灰色
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 笔记标签关联表 (多对多关系)
CREATE TABLE IF NOT EXISTS note_tags (
    note_id INTEGER NOT NULL,
    tag_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (note_id, tag_id),
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_notes_updated_at ON notes(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_notes_created_at ON notes(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notes_is_favorite ON notes(is_favorite);
CREATE INDEX IF NOT EXISTS idx_notes_title ON notes(title);
CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
CREATE INDEX IF NOT EXISTS idx_note_tags_note_id ON note_tags(note_id);
CREATE INDEX IF NOT EXISTS idx_note_tags_tag_id ON note_tags(tag_id);

-- 全文搜索虚拟表 (FTS5)
-- 用于高效的全文搜索功能
CREATE VIRTUAL TABLE IF NOT EXISTS notes_fts USING fts5(
    title, 
    content, 
    content='notes', 
    content_rowid='id',
    tokenize='unicode61 remove_diacritics 1'
);

-- 触发器：当笔记插入时，同步到FTS表
CREATE TRIGGER IF NOT EXISTS notes_fts_insert AFTER INSERT ON notes BEGIN
    INSERT INTO notes_fts(rowid, title, content) VALUES (new.id, new.title, new.content);
END;

-- 触发器：当笔记更新时，同步到FTS表
CREATE TRIGGER IF NOT EXISTS notes_fts_update AFTER UPDATE ON notes BEGIN
    UPDATE notes_fts SET title = new.title, content = new.content WHERE rowid = new.id;
END;

-- 触发器：当笔记删除时，从FTS表删除
CREATE TRIGGER IF NOT EXISTS notes_fts_delete AFTER DELETE ON notes BEGIN
    DELETE FROM notes_fts WHERE rowid = old.id;
END;

-- 触发器：自动更新 updated_at 字段
CREATE TRIGGER IF NOT EXISTS notes_updated_at AFTER UPDATE ON notes BEGIN
    UPDATE notes SET updated_at = CURRENT_TIMESTAMP WHERE id = new.id;
END;

-- 插入一些默认标签（可选）
INSERT OR IGNORE INTO tags (name, color) VALUES 
    ('工作', '#EF4444'),
    ('学习', '#3B82F6'),
    ('生活', '#10B981'),
    ('重要', '#F59E0B'),
    ('想法', '#8B5CF6'),
    ('代码', '#6366F1'),
    ('笔记', '#6B7280');

-- 数据库版本信息表（用于迁移管理）
CREATE TABLE IF NOT EXISTS db_version (
    version INTEGER PRIMARY KEY,
    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

-- 插入初始版本信息
INSERT OR IGNORE INTO db_version (version, description) VALUES 
    (1, 'Initial database schema with notes, tags, and full-text search');
