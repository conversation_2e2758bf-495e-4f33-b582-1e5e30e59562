{"rustc": 1842507548689473721, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 6355579909791343455, "path": 15998243663244707169, "deps": [[325572602735163265, "tracing_attributes", false, 5714305416663500624], [1906322745568073236, "pin_project_lite", false, 4093451773802898845], [3424551429995674438, "tracing_core", false, 10696466478096826847], [5986029879202738730, "log", false, 13360595710959020976]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-dae2cbbd910095b0\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}