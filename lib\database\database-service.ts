/**
 * 数据库服务抽象层
 * 提供统一的数据库操作接口，支持 Tauri (桌面) 和 Capacitor (移动端) 双平台
 */

import type {
  Note,
  Tag,
  CreateNoteInput,
  UpdateNoteInput,
  CreateTagInput,
  UpdateTagInput,
  NoteQueryParams,
  QueryResult,
  SearchResult,
  DatabaseStatus,
  DatabaseConfig,
  DatabaseStats,
  Migration,
  ExportData,
  ImportOptions,
  ImportResult,
  Platform
} from './types'

/**
 * 数据库服务接口
 * 所有平台的具体实现都必须实现这个接口
 */
export interface DatabaseService {
  // ============= 连接和初始化 =============
  
  /**
   * 初始化数据库连接
   */
  initialize(config?: DatabaseConfig): Promise<void>
  
  /**
   * 关闭数据库连接
   */
  close(): Promise<void>
  
  /**
   * 获取数据库状态
   */
  getStatus(): DatabaseStatus
  
  /**
   * 获取当前平台
   */
  getPlatform(): Platform
  
  /**
   * 检查数据库是否可用
   */
  isAvailable(): Promise<boolean>

  // ============= 笔记操作 =============
  
  /**
   * 创建新笔记
   */
  createNote(input: CreateNoteInput): Promise<Note>
  
  /**
   * 根据ID获取笔记
   */
  getNoteById(id: number): Promise<Note | null>
  
  /**
   * 更新笔记
   */
  updateNote(id: number, input: UpdateNoteInput): Promise<Note>
  
  /**
   * 删除笔记
   */
  deleteNote(id: number): Promise<void>
  
  /**
   * 查询笔记列表
   */
  queryNotes(params?: NoteQueryParams): Promise<QueryResult<Note>>
  
  /**
   * 全文搜索笔记
   */
  searchNotes(searchTerm: string, params?: NoteQueryParams): Promise<SearchResult[]>
  
  /**
   * 获取收藏的笔记
   */
  getFavoriteNotes(params?: NoteQueryParams): Promise<QueryResult<Note>>
  
  /**
   * 获取无标签的笔记
   */
  getUntaggedNotes(params?: NoteQueryParams): Promise<QueryResult<Note>>
  
  /**
   * 切换笔记收藏状态
   */
  toggleNoteFavorite(id: number): Promise<Note>

  // ============= 标签操作 =============
  
  /**
   * 创建新标签
   */
  createTag(input: CreateTagInput): Promise<Tag>
  
  /**
   * 根据ID获取标签
   */
  getTagById(id: number): Promise<Tag | null>
  
  /**
   * 根据名称获取标签
   */
  getTagByName(name: string): Promise<Tag | null>
  
  /**
   * 更新标签
   */
  updateTag(id: number, input: UpdateTagInput): Promise<Tag>
  
  /**
   * 删除标签
   */
  deleteTag(id: number): Promise<void>
  
  /**
   * 获取所有标签
   */
  getAllTags(): Promise<Tag[]>
  
  /**
   * 获取标签使用统计
   */
  getTagStats(): Promise<Tag[]>

  // ============= 笔记标签关联操作 =============
  
  /**
   * 为笔记添加标签
   */
  addTagToNote(noteId: number, tagId: number): Promise<void>
  
  /**
   * 从笔记移除标签
   */
  removeTagFromNote(noteId: number, tagId: number): Promise<void>
  
  /**
   * 设置笔记的标签（替换所有现有标签）
   */
  setNoteTags(noteId: number, tagIds: number[]): Promise<void>
  
  /**
   * 获取笔记的所有标签
   */
  getNoteTagsByNoteId(noteId: number): Promise<Tag[]>
  
  /**
   * 获取使用指定标签的所有笔记
   */
  getNotesByTagId(tagId: number, params?: NoteQueryParams): Promise<QueryResult<Note>>

  // ============= 数据统计 =============
  
  /**
   * 获取数据库统计信息
   */
  getStats(): Promise<DatabaseStats>
  
  /**
   * 获取笔记数量
   */
  getNotesCount(): Promise<number>
  
  /**
   * 获取标签数量
   */
  getTagsCount(): Promise<number>

  // ============= 数据库维护 =============
  
  /**
   * 执行数据库迁移
   */
  migrate(): Promise<void>
  
  /**
   * 获取当前数据库版本
   */
  getCurrentVersion(): Promise<number>
  
  /**
   * 获取迁移历史
   */
  getMigrationHistory(): Promise<Migration[]>
  
  /**
   * 清理数据库（删除所有数据）
   */
  clearDatabase(): Promise<void>
  
  /**
   * 优化数据库（重建索引、清理空间等）
   */
  optimizeDatabase(): Promise<void>

  // ============= 数据导入导出 =============
  
  /**
   * 导出所有数据
   */
  exportData(): Promise<ExportData>
  
  /**
   * 导入数据
   */
  importData(data: ExportData, options?: ImportOptions): Promise<ImportResult>
  
  /**
   * 备份数据库
   */
  backup(): Promise<string> // 返回备份文件路径或数据
  
  /**
   * 从备份恢复数据库
   */
  restore(backupData: string): Promise<void>

  // ============= 事件监听 =============
  
  /**
   * 监听数据库状态变化
   */
  onStatusChange(callback: (status: DatabaseStatus) => void): () => void
  
  /**
   * 监听笔记变化
   */
  onNoteChange(callback: (event: 'created' | 'updated' | 'deleted', note: Note) => void): () => void
  
  /**
   * 监听标签变化
   */
  onTagChange(callback: (event: 'created' | 'updated' | 'deleted', tag: Tag) => void): () => void
}

/**
 * 数据库服务工厂
 * 根据当前运行环境创建对应的数据库服务实例
 */
export class DatabaseServiceFactory {
  private static instance: DatabaseService | null = null
  
  /**
   * 获取数据库服务实例（单例模式）
   */
  static async getInstance(): Promise<DatabaseService> {
    if (!this.instance) {
      this.instance = await this.createService()
    }
    return this.instance
  }
  
  /**
   * 重置实例（主要用于测试）
   */
  static resetInstance(): void {
    this.instance = null
  }
  
  /**
   * 根据环境创建对应的服务实例
   */
  private static async createService(): Promise<DatabaseService> {
    // 检测运行环境
    if (typeof window !== 'undefined') {
      // 浏览器环境
      if ((window as any).__TAURI__) {
        // Tauri 环境
        const { TauriDatabaseService } = await import('./tauri-database-service')
        return new TauriDatabaseService()
      } else if ((window as any).Capacitor) {
        // Capacitor 环境
        const { CapacitorDatabaseService } = await import('./capacitor-database-service')
        return new CapacitorDatabaseService()
      } else {
        // Web 环境（开发/测试）
        const { WebDatabaseService } = await import('./web-database-service')
        return new WebDatabaseService()
      }
    }
    
    // Node.js 环境（不应该出现，但提供后备方案）
    throw new Error('Unsupported environment for database service')
  }
}

/**
 * 获取数据库服务实例的便捷函数
 */
export const getDatabase = () => DatabaseServiceFactory.getInstance()
