{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_sqlite\", \"any\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"runtime-tokio\", \"sqlite\", \"sqlx-macros\", \"sqlx-sqlite\", \"time\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlite-preupdate-hook\", \"sqlite-unbundled\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"tls-rustls-aws-lc-rs\", \"tls-rustls-ring\", \"tls-rustls-ring-native-roots\", \"tls-rustls-ring-webpki\", \"uuid\"]", "target": 3003836824758849296, "profile": 15657897354478470176, "path": 2264581583437310286, "deps": [[3276107248499827220, "sqlx_macros", false, 18116003352364066410], [10776111606377762245, "sqlx_core", false, 7594457549114395717], [17038106176255014628, "sqlx_sqlite", false, 4996837954310545561]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-d1a73cccb296f552\\dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}