{"rustc": 1842507548689473721, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 12257077738785796254, "deps": [[5103565458935487, "futures_io", false, 5608480785377085168], [1615478164327904835, "pin_utils", false, 8083862251042668308], [1906322745568073236, "pin_project_lite", false, 5237517042048274452], [5451793922601807560, "slab", false, 4296045695481154095], [7013762810557009322, "futures_sink", false, 13614615935110020869], [7620660491849607393, "futures_core", false, 1822698864488623242], [15932120279885307830, "memchr", false, 11261746297635178691], [16240732885093539806, "futures_task", false, 245777684709881873]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-d5ced993103eb248\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}