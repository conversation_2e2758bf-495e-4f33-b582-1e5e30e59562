# 数据库架构设计

这是笔记应用的 SQLite 数据库架构，支持 Tauri (桌面) 和 Capacitor (移动端) 双平台。

## 架构概览

```
lib/database/
├── types.ts              # TypeScript 类型定义
├── database-service.ts   # 数据库服务抽象层
├── schema.sql           # 数据库表结构定义
├── migrations.ts        # 数据库迁移管理
├── init.ts             # 数据库初始化
├── index.ts            # 模块入口文件
├── example-usage.ts    # 使用示例
└── README.md           # 本文档
```

## 数据库表结构

### 1. notes 表 - 笔记数据
```sql
CREATE TABLE notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL DEFAULT '无标题',
    content TEXT DEFAULT '',
    is_favorite BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 2. tags 表 - 标签数据
```sql
CREATE TABLE tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    color TEXT DEFAULT '#6B7280',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 3. note_tags 表 - 笔记标签关联
```sql
CREATE TABLE note_tags (
    note_id INTEGER NOT NULL,
    tag_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (note_id, tag_id),
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);
```

### 4. notes_fts 表 - 全文搜索索引
```sql
CREATE VIRTUAL TABLE notes_fts USING fts5(
    title, 
    content, 
    content='notes', 
    content_rowid='id',
    tokenize='unicode61 remove_diacritics 1'
);
```

## 核心特性

### 1. 跨平台支持
- **Tauri (桌面)**：使用 `tauri-plugin-sql`
- **Capacitor (移动端)**：使用 `@capacitor-community/sqlite`
- **Web (开发)**：使用 `sql.js` 或 IndexedDB 替代

### 2. 全文搜索
- 基于 SQLite FTS5 的高性能全文搜索
- 支持中文分词和模糊匹配
- 自动同步索引更新

### 3. 数据迁移
- 从 localStorage 自动迁移现有数据
- 版本化数据库迁移系统
- 数据完整性保证

### 4. 性能优化
- 合理的索引设计
- 查询优化
- 分页支持
- 延迟加载

## 使用方法

### 1. 应用初始化
```typescript
import { initializeDatabase } from '@/lib/database'

// 在应用启动时调用
await initializeDatabase({
  enableMigration: true,
  enableLocalStorageMigration: true,
  onProgress: (step, progress) => {
    console.log(`${step} (${progress}%)`)
  }
})
```

### 2. 基本操作
```typescript
import { getDatabase } from '@/lib/database'

const db = await getDatabase()

// 创建笔记
const note = await db.createNote({
  title: '我的笔记',
  content: '笔记内容',
  isFavorite: false
})

// 搜索笔记
const results = await db.searchNotes('搜索关键词')

// 创建标签
const tag = await db.createTag({ name: '工作', color: '#EF4444' })
```

### 3. 在 React 中使用
```typescript
import { useDatabase } from '@/lib/database/example-usage'

function MyComponent() {
  const { createNote, searchNotes, getAllTagsWithStats } = useDatabase()
  
  // 使用数据库操作...
}
```

## 数据库服务接口

### 核心方法
- `initialize()` - 初始化数据库连接
- `createNote()` - 创建笔记
- `updateNote()` - 更新笔记
- `deleteNote()` - 删除笔记
- `searchNotes()` - 全文搜索
- `createTag()` - 创建标签
- `setNoteTags()` - 设置笔记标签

### 查询方法
- `queryNotes()` - 查询笔记列表
- `getFavoriteNotes()` - 获取收藏笔记
- `getUntaggedNotes()` - 获取无标签笔记
- `getNotesByTagId()` - 按标签查询笔记

### 统计方法
- `getStats()` - 获取数据库统计
- `getTagStats()` - 获取标签使用统计

### 维护方法
- `migrate()` - 执行数据库迁移
- `backup()` - 创建备份
- `restore()` - 从备份恢复
- `optimizeDatabase()` - 优化数据库

## 下一步实施计划

### 第二阶段：Tauri 端实现
1. 添加 `tauri-plugin-sql` 依赖
2. 实现 `TauriDatabaseService` 类
3. 配置权限和安全策略

### 第三阶段：Capacitor 端实现
1. 添加 `@capacitor-community/sqlite` 依赖
2. 实现 `CapacitorDatabaseService` 类
3. 配置移动端权限

### 第四阶段：Web 端实现
1. 实现 `WebDatabaseService` 类
2. 使用 `sql.js` 或 IndexedDB
3. 开发环境支持

### 第五阶段：集成和测试
1. 更新现有组件使用新的数据库服务
2. 数据迁移测试
3. 性能优化和错误处理

## 优势总结

1. **统一接口**：所有平台使用相同的 API
2. **类型安全**：完整的 TypeScript 类型定义
3. **高性能**：SQLite + FTS5 全文搜索
4. **数据安全**：事务支持和数据完整性
5. **易于维护**：清晰的架构和文档
6. **可扩展**：支持未来功能扩展

这个架构为笔记应用提供了坚实的数据存储基础，支持从简单的本地存储到复杂的多平台数据同步需求。
