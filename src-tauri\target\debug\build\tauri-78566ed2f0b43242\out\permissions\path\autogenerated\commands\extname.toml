# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-extname"
description = "Enables the extname command without any pre-configured scope."
commands.allow = ["extname"]

[[permission]]
identifier = "deny-extname"
description = "Denies the extname command without any pre-configured scope."
commands.deny = ["extname"]
