{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 9831540453462500854, "deps": [[376837177317575824, "softbuffer", false, 14857568649156715958], [654232091421095663, "tauri_utils", false, 13712148212591670789], [2013030631243296465, "webview2_com", false, 14951264712843224681], [3150220818285335163, "url", false, 996284761323975504], [3722963349756955755, "once_cell", false, 4515980865749838026], [4143744114649553716, "raw_window_handle", false, 13056677237480622340], [5986029879202738730, "log", false, 8722473586600162715], [8826339825490770380, "tao", false, 17851189597512599596], [9010263965687315507, "http", false, 4601769349494712840], [9141053277961803901, "wry", false, 14840313078367873294], [12304025191202589669, "build_script_build", false, 3892786894844389636], [12943761728066819757, "tauri_runtime", false, 1628123000452858896], [14585479307175734061, "windows", false, 3701177281461759728]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-d9846d5d08c2303d\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}