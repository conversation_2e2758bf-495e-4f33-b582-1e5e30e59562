{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_sqlite\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"time\", \"tokio\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 2175425913391121376, "path": 16403189589140859079, "deps": [[530211389790465181, "hex", false, 16407604029848920356], [3060637413840920116, "proc_macro2", false, 12829056815806260006], [3150220818285335163, "url", false, 2227905525283752008], [3405707034081185165, "dotenvy", false, 2720819516109230310], [3722963349756955755, "once_cell", false, 15951163895981159829], [4974441333307933176, "syn", false, 2939883612788580963], [8569119365930580996, "serde_json", false, 9801121442321344201], [9689903380558560274, "serde", false, 2747036541906619410], [9857275760291862238, "sha2", false, 9071165338192426708], [10776111606377762245, "sqlx_core", false, 13100294316342136224], [12170264697963848012, "either", false, 7478210577403104591], [12393800526703971956, "tokio", false, 6860374530683584926], [13077543566650298139, "heck", false, 3833667618904260674], [17038106176255014628, "sqlx_sqlite", false, 11334470967992761153], [17990358020177143287, "quote", false, 7892140333893364853]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-core-83850780b5bdb18f\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}