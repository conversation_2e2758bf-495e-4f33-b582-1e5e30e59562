cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=E:\studycode\sky\sky_react\jdnotes\src-tauri\target\debug\build\tauri-plugin-sql-cc5ce3a79f906641\out\tauri-plugin-sql-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\D:\tools\rust\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-sql-2.3.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
