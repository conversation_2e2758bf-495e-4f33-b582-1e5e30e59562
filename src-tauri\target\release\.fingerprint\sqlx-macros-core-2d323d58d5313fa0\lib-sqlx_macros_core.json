{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_sqlite\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"time\", \"tokio\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 1913379758057723763, "path": 16403189589140859079, "deps": [[530211389790465181, "hex", false, 13890048991347382785], [3060637413840920116, "proc_macro2", false, 5833869361467354012], [3150220818285335163, "url", false, 4920937753471628383], [3405707034081185165, "dotenvy", false, 486670496229796061], [3722963349756955755, "once_cell", false, 7431131288183913915], [4974441333307933176, "syn", false, 16273345053703310465], [8569119365930580996, "serde_json", false, 15384600167726692329], [9689903380558560274, "serde", false, 9456690565758718592], [9857275760291862238, "sha2", false, 8807553530799729956], [10776111606377762245, "sqlx_core", false, 4609282867303978308], [12170264697963848012, "either", false, 11636299753303276653], [12393800526703971956, "tokio", false, 13733333792993173036], [13077543566650298139, "heck", false, 17273572030905112929], [17038106176255014628, "sqlx_sqlite", false, 9163668178541859693], [17990358020177143287, "quote", false, 7063110427199514833]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\sqlx-macros-core-2d323d58d5313fa0\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}