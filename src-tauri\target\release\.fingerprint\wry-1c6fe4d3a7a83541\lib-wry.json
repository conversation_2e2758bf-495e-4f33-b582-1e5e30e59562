{"rustc": 1842507548689473721, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 1554061257607164671, "path": 18395026236560453371, "deps": [[2013030631243296465, "webview2_com", false, 14951264712843224681], [3334271191048661305, "windows_version", false, 15745963127238769393], [3722963349756955755, "once_cell", false, 4515980865749838026], [4143744114649553716, "raw_window_handle", false, 13056677237480622340], [5628259161083531273, "windows_core", false, 9097161324128413733], [7606335748176206944, "dpi", false, 7374436707499778977], [9010263965687315507, "http", false, 4601769349494712840], [9141053277961803901, "build_script_build", false, 4479981044421161396], [10806645703491011684, "thiserror", false, 16969897230673062756], [11989259058781683633, "dunce", false, 1794205478168231105], [14585479307175734061, "windows", false, 3701177281461759728], [16727543399706004146, "cookie", false, 11684825410004884960]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wry-1c6fe4d3a7a83541\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}