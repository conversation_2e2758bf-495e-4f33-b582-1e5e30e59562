{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"sqlite\", \"time\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 14211260863105982606, "path": 16895545119748951556, "deps": [[3060637413840920116, "proc_macro2", false, 5833869361467354012], [4974441333307933176, "syn", false, 16273345053703310465], [10654871823602349891, "sqlx_macros_core", false, 2247028364666288115], [10776111606377762245, "sqlx_core", false, 4609282867303978308], [17990358020177143287, "quote", false, 7063110427199514833]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\sqlx-macros-376e9bc165ed3053\\dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}