{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 14661550995293435084, "deps": [[40386456601120721, "percent_encoding", false, 5246120859346668611], [654232091421095663, "tauri_utils", false, 17816353002850615878], [1200537532907108615, "url<PERSON><PERSON>n", false, 13027167489387869497], [2013030631243296465, "webview2_com", false, 14716284576735372477], [3150220818285335163, "url", false, 5391303607643370604], [3331586631144870129, "getrandom", false, 922718123510441438], [4143744114649553716, "raw_window_handle", false, 8871094038573090994], [4494683389616423722, "muda", false, 14492932686449651380], [4919829919303820331, "serialize_to_javascript", false, 948528155810766419], [5986029879202738730, "log", false, 13360595710959020976], [8569119365930580996, "serde_json", false, 16260449719180404851], [9010263965687315507, "http", false, 4609389182342060280], [9689903380558560274, "serde", false, 2747036541906619410], [10229185211513642314, "mime", false, 4472229105638937915], [10806645703491011684, "thiserror", false, 4162130665435751246], [11989259058781683633, "dunce", false, 18087009893956605591], [12092653563678505622, "build_script_build", false, 15505707018007802362], [12304025191202589669, "tauri_runtime_wry", false, 17667043650179554893], [12393800526703971956, "tokio", false, 3583243449962576455], [12565293087094287914, "window_vibrancy", false, 5323440510663890994], [12943761728066819757, "tauri_runtime", false, 2243538292417931881], [12986574360607194341, "serde_repr", false, 17413527038979807403], [13077543566650298139, "heck", false, 3833667618904260674], [13405681745520956630, "tauri_macros", false, 15685639548764289965], [13625485746686963219, "anyhow", false, 13363454249419097510], [14585479307175734061, "windows", false, 16572681052828898213], [16928111194414003569, "dirs", false, 1609515387622353247], [17155886227862585100, "glob", false, 14311527598433297175]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-2086a073d3b8d121\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}