["\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-78566ed2f0b43242\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-78566ed2f0b43242\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-78566ed2f0b43242\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-78566ed2f0b43242\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-78566ed2f0b43242\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-78566ed2f0b43242\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-78566ed2f0b43242\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-78566ed2f0b43242\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-78566ed2f0b43242\\out\\permissions\\path\\autogenerated\\default.toml"]