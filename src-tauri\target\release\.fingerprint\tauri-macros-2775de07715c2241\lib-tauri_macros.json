{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 17110922245408071272, "deps": [[654232091421095663, "tauri_utils", false, 8150925576510720657], [2704937418414716471, "tauri_codegen", false, 17177173465084080049], [3060637413840920116, "proc_macro2", false, 5833869361467354012], [4974441333307933176, "syn", false, 16273345053703310465], [13077543566650298139, "heck", false, 17273572030905112929], [17990358020177143287, "quote", false, 7063110427199514833]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-2775de07715c2241\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}