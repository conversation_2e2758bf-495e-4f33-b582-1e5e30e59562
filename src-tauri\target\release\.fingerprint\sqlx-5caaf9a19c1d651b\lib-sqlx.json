{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_sqlite\", \"any\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"runtime-tokio\", \"sqlite\", \"sqlx-macros\", \"sqlx-sqlite\", \"time\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlite-preupdate-hook\", \"sqlite-unbundled\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"tls-rustls-aws-lc-rs\", \"tls-rustls-ring\", \"tls-rustls-ring-native-roots\", \"tls-rustls-ring-webpki\", \"uuid\"]", "target": 3003836824758849296, "profile": 2040997289075261528, "path": 2264581583437310286, "deps": [[3276107248499827220, "sqlx_macros", false, 7657464498904777229], [10776111606377762245, "sqlx_core", false, 5868048443882776166], [17038106176255014628, "sqlx_sqlite", false, 11601113149612210674]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\sqlx-5caaf9a19c1d651b\\dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}