{"rustc": 1842507548689473721, "features": "[\"barrier\", \"default\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"rwlock\", \"spin_mutex\"]", "declared_features": "[\"barrier\", \"default\", \"fair_mutex\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"portable-atomic\", \"portable_atomic\", \"rwlock\", \"spin_mutex\", \"std\", \"ticket_mutex\", \"use_ticket_mutex\"]", "target": 4260413527236709406, "profile": 1369601567987815722, "path": 3570597637223649619, "deps": [[8081351675046095464, "lock_api_crate", false, 18076481242164209380]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\spin-5005a72b59530530\\dep-lib-spin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}