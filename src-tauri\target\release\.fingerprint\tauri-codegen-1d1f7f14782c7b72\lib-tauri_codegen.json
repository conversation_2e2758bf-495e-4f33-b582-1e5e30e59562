{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 8879837628796626640, "deps": [[654232091421095663, "tauri_utils", false, 8150925576510720657], [3060637413840920116, "proc_macro2", false, 5833869361467354012], [3150220818285335163, "url", false, 4920937753471628383], [4899080583175475170, "semver", false, 4276315326520128156], [4974441333307933176, "syn", false, 16273345053703310465], [7170110829644101142, "json_patch", false, 16389229318033198903], [7392050791754369441, "ico", false, 10215503634606366760], [8319709847752024821, "uuid", false, 1810398405112657693], [8569119365930580996, "serde_json", false, 15384600167726692329], [9556762810601084293, "brotli", false, 17432405951254446789], [9689903380558560274, "serde", false, 9456690565758718592], [9857275760291862238, "sha2", false, 8807553530799729956], [10806645703491011684, "thiserror", false, 1681319421794752730], [12687914511023397207, "png", false, 11332526338420078312], [13077212702700853852, "base64", false, 6161113126714645131], [15622660310229662834, "walkdir", false, 350438185557728416], [17990358020177143287, "quote", false, 7063110427199514833]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-1d1f7f14782c7b72\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}