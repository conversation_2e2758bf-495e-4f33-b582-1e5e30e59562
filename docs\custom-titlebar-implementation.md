# Tauri 2 自定义标题栏实现指南

## 问题背景

在使用 Tauri 2 构建桌面应用时，遇到了以下问题：
1. 默认的系统标题栏与应用设计风格不协调，显得割裂
2. 构建的 exe 文件中自定义标题栏不显示
3. 页面出现多个滚动条，影响用户体验
4. 编辑区域高度不能随窗口大小自适应

## 解决方案概述

### 1. 自定义标题栏不显示问题

**问题分析：**
- Tauri 配置正确（`decorations: false`）
- 自定义标题栏组件代码正常
- 但在生产构建中标题栏不显示

**根本原因：**
1. **Rust 代码缺少必要的 trait 导入**
2. **Tauri 环境检测在生产环境中失效**
3. **动态导入的 Tauri API 在构建时出现问题**

**解决步骤：**

#### 步骤 1: 修复 Rust 代码导入问题
```rust
// src-tauri/src/lib.rs
use tauri::Manager;  // 添加这行导入

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .setup(|app| {
      // ... 其他代码
      let window = app.get_webview_window("main").unwrap();
      // ... 窗口事件监听
    })
    // ...
}
```

**错误信息：**
```
error[E0599]: no method named `get_webview_window` found for mutable reference `&mut tauri::App`
help: trait `Manager` which provides `get_webview_window` is implemented but not in scope
```

#### 步骤 2: 完善权限配置
```json
// src-tauri/capabilities/default.json
{
  "permissions": [
    "core:default",
    "core:window:allow-close",
    "core:window:allow-minimize", 
    "core:window:allow-maximize",
    "core:window:allow-unmaximize",
    "core:window:allow-toggle-maximize",
    "core:window:allow-start-dragging"
  ]
}
```

#### 步骤 3: 优化 Tauri 环境检测
创建可靠的环境检测 Hook：

```typescript
// hooks/use-tauri.ts
export function useTauri() {
  const [isTauri, setIsTauri] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkTauri = async () => {
      try {
        if (typeof window !== "undefined") {
          // 多种方式检测 Tauri 环境
          const hasTauriGlobal = window.__TAURI__ !== undefined
          const hasTauriAPI = window.__TAURI_INTERNALS__ !== undefined
          
          // 尝试导入 Tauri API 来进一步确认
          let hasTauriImport = false
          try {
            await import("@tauri-apps/api/core")
            hasTauriImport = true
          } catch {
            hasTauriImport = false
          }
          
          const isInTauri = hasTauriGlobal || hasTauriAPI || hasTauriImport
          setIsTauri(isInTauri)
        }
      } catch (error) {
        setIsTauri(false)
      } finally {
        setIsLoading(false)
      }
    }

    // 延迟检测，确保 Tauri 完全初始化
    const timer = setTimeout(checkTauri, 100)
    return () => clearTimeout(timer)
  }, [])

  return { isTauri, isLoading }
}
```

#### 步骤 4: 创建简化的标题栏组件
为了确保在生产环境中的可靠性，创建了一个简化版本：

```typescript
// components/simple-titlebar.tsx
export function SimpleTitlebar({ title = "简单笔记" }: SimpleTitlebarProps) {
  const [isTauri, setIsTauri] = useState(false)

  useEffect(() => {
    // 简单的 Tauri 检测
    const checkTauri = () => {
      if (typeof window !== "undefined") {
        const hasTauri = window.__TAURI__ !== undefined || window.__TAURI_INTERNALS__ !== undefined
        setIsTauri(hasTauri)
      }
    }
    checkTauri()
  }, [])

  // 窗口控制函数
  const handleMinimize = async () => {
    try {
      const { getCurrentWindow } = await import("@tauri-apps/api/window")
      await getCurrentWindow().minimize()
    } catch (error) {
      console.error("Failed to minimize:", error)
    }
  }
  
  // ... 其他控制函数

  if (!isTauri) return null

  return (
    <div className="flex items-center justify-between h-10 bg-gray-50 dark:bg-background border-b">
      {/* 拖拽区域和窗口控制按钮 */}
    </div>
  )
}
```

#### 步骤 5: 集成应用图标
```typescript
// components/app-icon.tsx
export function AppIcon({ size = 20 }: AppIconProps) {
  const [imageError, setImageError] = useState(false)

  if (imageError) {
    // 备用图标
    return (
      <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-sm">
        <span>📝</span>
      </div>
    )
  }

  return (
    <img
      src="/app-icon-32.png"
      alt="应用图标"
      onError={() => setImageError(true)}
      style={{ width: size, height: size }}
    />
  )
}
```

### 2. 滚动条问题解决

**问题：** 页面出现多个默认滚动条，与设计不协调

**解决方案：** 统一使用项目的 ScrollArea 组件

#### 编辑模式滚动条修复：
```typescript
// 原来的代码 - 有问题
<Textarea className="min-h-[300px] overflow-auto" />

// 修复后的代码
<div className="flex-1 border rounded-md overflow-hidden">
  <ScrollArea className="h-full w-full">
    <div className="p-3">
      <textarea
        className="w-full resize-none border-0 bg-transparent overflow-hidden"
        style={{ 
          height: `${Math.max(300, editContent.split('\n').length * 24 + 100)}px`,
          minHeight: '100%'
        }}
      />
    </div>
  </ScrollArea>
</div>
```

#### 查看模式滚动条修复：
```typescript
// 修复前
<div className="prose prose-sm max-w-none">
  {/* 内容 */}
</div>

// 修复后
<div className="flex-1 min-h-0">
  <ScrollArea className="h-full w-full">
    <div className="prose prose-sm max-w-none pr-4">
      {/* 内容 */}
    </div>
  </ScrollArea>
</div>
```

### 3. 响应式高度问题解决

**问题：** 编辑区域使用固定高度，不能随窗口大小调整

**解决方案：** 使用 Flexbox 布局实现自适应

```typescript
// 修复前 - 固定高度
<div className="space-y-4 h-full">
  <Input /> {/* 标题 */}
  <Input /> {/* 标签 */}
  <div className="h-[300px]"> {/* 固定高度 */}
    <textarea />
  </div>
</div>

// 修复后 - 自适应高度
<div className="flex flex-col h-full gap-4">
  <Input /> {/* 标题 */}
  <Input /> {/* 标签 */}
  <div className="flex-1 min-h-0"> {/* 占用剩余空间 */}
    <ScrollArea className="h-full">
      <textarea />
    </ScrollArea>
  </div>
</div>
```

## 关键技术要点

### 1. Tauri 2 环境检测最佳实践
- 使用多种方式检测（`__TAURI__`、`__TAURI_INTERNALS__`、动态导入）
- 添加延迟检测，确保 Tauri 完全初始化
- 提供加载状态和错误处理

### 2. 窗口控制 API 使用
```typescript
// 正确的动态导入方式
const { getCurrentWindow } = await import("@tauri-apps/api/window")
const window = getCurrentWindow()

// 窗口操作
await window.minimize()
await window.toggleMaximize()
await window.close()
await window.startDragging()
```

### 3. CSS 拖拽区域设置
```css
/* 可拖拽区域 */
-webkit-app-region: drag;

/* 不可拖拽区域（按钮等） */
-webkit-app-region: no-drag;
```

### 4. Flexbox 布局关键点
- 使用 `flex-1` 让元素占用剩余空间
- 使用 `min-h-0` 防止 flex 子元素溢出
- 使用 `overflow-hidden` 控制滚动行为

## 测试验证

### 开发环境测试
```bash
npm run tauri dev
```

### 生产构建测试
```bash
npm run build
npm run tauri build
```

### 验证要点
1. ✅ 标题栏在桌面应用中正常显示
2. ✅ Web 版本不显示标题栏
3. ✅ 窗口控制按钮功能正常
4. ✅ 拖拽移动窗口正常
5. ✅ 滚动条样式统一
6. ✅ 编辑区域高度自适应

## 故障排除指南

### 常见问题及解决方案

#### 1. 标题栏不显示
**症状：** 构建的 exe 文件中看不到自定义标题栏

**排查步骤：**
```bash
# 1. 检查 Rust 编译错误
npm run tauri dev

# 2. 检查浏览器控制台
# 打开开发者工具，查看是否有 JavaScript 错误

# 3. 检查环境检测
console.log('Tauri detected:', window.__TAURI__ !== undefined)
```

**可能原因及解决方案：**
- ❌ 缺少 `use tauri::Manager;` → ✅ 添加导入
- ❌ 权限配置不完整 → ✅ 检查 `capabilities/default.json`
- ❌ 环境检测失败 → ✅ 使用多重检测机制
- ❌ 动态导入失败 → ✅ 添加错误处理

#### 2. 窗口控制按钮无响应
**症状：** 点击最小化、最大化、关闭按钮没有反应

**解决方案：**
```typescript
// 添加详细的错误日志
const handleMinimize = async () => {
  try {
    console.log('Attempting to minimize window...')
    const { getCurrentWindow } = await import("@tauri-apps/api/window")
    const window = getCurrentWindow()
    await window.minimize()
    console.log('Window minimized successfully')
  } catch (error) {
    console.error("Failed to minimize:", error)
    // 显示用户友好的错误信息
  }
}
```

#### 3. 拖拽功能不工作
**症状：** 无法通过标题栏拖拽移动窗口

**检查要点：**
```css
/* 确保拖拽区域设置正确 */
.titlebar-drag-region {
  -webkit-app-region: drag;
}

.titlebar-button {
  -webkit-app-region: no-drag;
}
```

### 调试技巧

#### 1. 开发环境调试信息
```typescript
// 在标题栏组件中添加调试信息
{process.env.NODE_ENV === 'development' && (
  <div className="text-xs text-blue-600">
    Tauri: {isTauri ? '✅' : '❌'} |
    Loading: {isLoading ? '⏳' : '✅'}
  </div>
)}
```

#### 2. 生产环境日志
```typescript
// 添加生产环境的错误收集
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error)
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)
})
```

## 性能优化建议

### 1. 懒加载 Tauri API
```typescript
// 避免在模块顶层导入，使用动态导入
const getTauriWindow = async () => {
  const { getCurrentWindow } = await import("@tauri-apps/api/window")
  return getCurrentWindow()
}
```

### 2. 防抖窗口事件
```typescript
// 防止频繁的窗口状态检查
const debouncedCheckWindowState = useMemo(
  () => debounce(async () => {
    const window = await getTauriWindow()
    const isMaximized = await window.isMaximized()
    setIsMaximized(isMaximized)
  }, 100),
  []
)
```

### 3. 内存泄漏预防
```typescript
useEffect(() => {
  let cleanup: (() => void) | undefined

  const setupWindowListeners = async () => {
    const window = await getTauriWindow()
    const unlisten = await window.onResized(debouncedCheckWindowState)
    cleanup = unlisten
  }

  setupWindowListeners()

  return () => {
    if (cleanup) cleanup()
    debouncedCheckWindowState.cancel?.()
  }
}, [])
```

## 最佳实践总结

### 1. 代码组织
- 将 Tauri 相关逻辑封装在自定义 Hook 中
- 创建可复用的窗口控制组件
- 分离开发和生产环境的调试代码

### 2. 错误处理
- 为所有 Tauri API 调用添加 try-catch
- 提供优雅的降级方案
- 记录详细的错误信息用于调试

### 3. 用户体验
- 添加加载状态指示
- 提供视觉反馈（按钮悬停效果等）
- 确保键盘导航支持

### 4. 测试策略
- 在开发环境中测试所有功能
- 构建生产版本进行完整测试
- 在不同操作系统上验证兼容性

## 总结

解决自定义标题栏问题的关键在于：
1. **正确配置 Rust 代码和权限**
2. **可靠的环境检测机制**
3. **合理的组件设计和错误处理**
4. **统一的滚动条和布局方案**

通过系统性的问题分析和逐步解决，成功实现了与应用设计一体化的自定义标题栏，显著提升了用户体验。这个解决方案不仅解决了当前问题，还为未来的功能扩展奠定了良好的基础。
