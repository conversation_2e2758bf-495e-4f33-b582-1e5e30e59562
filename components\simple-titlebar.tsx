"use client"

import React, { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Minus, X, Maximize2, Minimize2 } from "lucide-react"
import { AppIcon } from "@/components/app-icon"

interface SimpleTitlebarProps {
  title?: string
}

export function SimpleTitlebar({ title = "简单笔记" }: SimpleTitlebarProps) {
  const [isTauri, setIsTauri] = useState(false)
  const [isMaximized, setIsMaximized] = useState(false)

  useEffect(() => {
    // 简单的 Tauri 检测
    const checkTauri = () => {
      if (typeof window !== "undefined") {
        // @ts-ignore
        const hasTauri = window.__TAURI__ !== undefined || window.__TAURI_INTERNALS__ !== undefined
        setIsTauri(hasTauri)
        console.log("Tauri detected:", hasTauri)
      }
    }

    checkTauri()
  }, [])

  const handleMinimize = async () => {
    try {
      const { getCurrentWindow } = await import("@tauri-apps/api/window")
      await getCurrentWindow().minimize()
    } catch (error) {
      console.error("Failed to minimize:", error)
    }
  }

  const handleMaximize = async () => {
    try {
      const { getCurrentWindow } = await import("@tauri-apps/api/window")
      const window = getCurrentWindow()
      await window.toggleMaximize()
      const maximized = await window.isMaximized()
      setIsMaximized(maximized)
    } catch (error) {
      console.error("Failed to maximize:", error)
    }
  }

  const handleClose = async () => {
    try {
      const { getCurrentWindow } = await import("@tauri-apps/api/window")
      await getCurrentWindow().close()
    } catch (error) {
      console.error("Failed to close:", error)
    }
  }

  const handleDrag = async () => {
    try {
      const { getCurrentWindow } = await import("@tauri-apps/api/window")
      await getCurrentWindow().startDragging()
    } catch (error) {
      console.error("Failed to start dragging:", error)
    }
  }

  // 只在 Tauri 环境中显示
  if (!isTauri) {
    return null
  }

  return (
    <div className="flex items-center justify-between h-10 bg-gray-50 dark:bg-background border-b border-gray-200 dark:border-border select-none">
      {/* 拖拽区域 */}
      <div 
        className="flex-1 h-full flex items-center px-4 cursor-move"
        onMouseDown={handleDrag}
      >
        <div className="flex items-center gap-3">
          <AppIcon size={20} />
          <span className="text-sm font-semibold text-gray-900 dark:text-foreground">
            {title}
          </span>
        </div>
      </div>

      {/* 窗口控制按钮 */}
      <div className="flex items-center h-full">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleMinimize}
          className="h-10 w-12 rounded-none hover:bg-gray-200 dark:hover:bg-gray-800"
        >
          <Minus className="h-3.5 w-3.5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleMaximize}
          className="h-10 w-12 rounded-none hover:bg-gray-200 dark:hover:bg-gray-800"
        >
          {isMaximized ? <Minimize2 className="h-3.5 w-3.5" /> : <Maximize2 className="h-3.5 w-3.5" />}
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-10 w-12 rounded-none hover:bg-red-500 hover:text-white"
        >
          <X className="h-3.5 w-3.5" />
        </Button>
      </div>
    </div>
  )
}
