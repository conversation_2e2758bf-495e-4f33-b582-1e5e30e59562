/**
 * 数据库相关的 TypeScript 类型定义
 * 支持 Tauri (桌面) 和 Capacitor (移动端) 双平台
 */

// ============= 基础数据模型 =============

/**
 * 笔记数据模型
 */
export interface Note {
  id: number
  title: string
  content: string
  isFavorite: boolean
  createdAt: Date
  updatedAt: Date
  tags?: Tag[] // 关联的标签列表（可选，用于查询结果）
}

/**
 * 创建笔记时的数据模型（不包含 id 和时间戳）
 */
export interface CreateNoteInput {
  title: string
  content: string
  isFavorite?: boolean
  tagIds?: number[] // 关联的标签ID列表
}

/**
 * 更新笔记时的数据模型（所有字段都是可选的）
 */
export interface UpdateNoteInput {
  title?: string
  content?: string
  isFavorite?: boolean
  tagIds?: number[] // 关联的标签ID列表
}

/**
 * 标签数据模型
 */
export interface Tag {
  id: number
  name: string
  color: string
  createdAt: Date
  noteCount?: number // 使用该标签的笔记数量（可选，用于统计）
}

/**
 * 创建标签时的数据模型
 */
export interface CreateTagInput {
  name: string
  color?: string
}

/**
 * 更新标签时的数据模型
 */
export interface UpdateTagInput {
  name?: string
  color?: string
}

/**
 * 笔记标签关联数据模型
 */
export interface NoteTag {
  noteId: number
  tagId: number
  createdAt: Date
}

// ============= 查询相关类型 =============

/**
 * 排序选项
 */
export type SortOption = 'updated' | 'created' | 'title' | 'favorite'

/**
 * 排序方向
 */
export type SortDirection = 'asc' | 'desc'

/**
 * 笔记查询参数
 */
export interface NoteQueryParams {
  // 搜索相关
  searchTerm?: string
  tagIds?: number[]
  isFavorite?: boolean
  
  // 排序相关
  sortBy?: SortOption
  sortDirection?: SortDirection
  
  // 分页相关
  limit?: number
  offset?: number
  
  // 时间范围
  createdAfter?: Date
  createdBefore?: Date
  updatedAfter?: Date
  updatedBefore?: Date
}

/**
 * 查询结果
 */
export interface QueryResult<T> {
  data: T[]
  total: number
  hasMore: boolean
}

/**
 * 全文搜索结果
 */
export interface SearchResult {
  note: Note
  rank: number // 搜索相关性评分
  snippet?: string // 搜索结果摘要
}

// ============= 数据库操作相关类型 =============

/**
 * 数据库连接状态
 */
export type DatabaseStatus = 'disconnected' | 'connecting' | 'connected' | 'error'

/**
 * 数据库配置
 */
export interface DatabaseConfig {
  name: string
  version: number
  location?: string // 数据库文件位置（仅桌面端）
}

/**
 * 数据库迁移信息
 */
export interface Migration {
  version: number
  description: string
  sql: string
  appliedAt?: Date
}

/**
 * 数据库统计信息
 */
export interface DatabaseStats {
  totalNotes: number
  totalTags: number
  favoriteNotes: number
  untaggedNotes: number
  databaseSize?: number // 数据库文件大小（字节）
  lastBackup?: Date
}

// ============= 错误处理类型 =============

/**
 * 数据库错误类型
 */
export enum DatabaseErrorType {
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  QUERY_ERROR = 'QUERY_ERROR',
  CONSTRAINT_ERROR = 'CONSTRAINT_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  DUPLICATE_ERROR = 'DUPLICATE_ERROR',
  MIGRATION_ERROR = 'MIGRATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 数据库错误
 */
export class DatabaseError extends Error {
  constructor(
    public type: DatabaseErrorType,
    message: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'DatabaseError'
  }
}

// ============= 平台特定类型 =============

/**
 * 平台类型
 */
export type Platform = 'tauri' | 'capacitor' | 'web'

/**
 * 平台特定的数据库实例类型
 */
export interface PlatformDatabase {
  platform: Platform
  isAvailable: boolean
  version?: string
}

// ============= 导入导出类型 =============

/**
 * 导出数据格式
 */
export interface ExportData {
  version: string
  exportedAt: Date
  notes: Note[]
  tags: Tag[]
  metadata: {
    totalNotes: number
    totalTags: number
    appVersion: string
  }
}

/**
 * 导入选项
 */
export interface ImportOptions {
  overwriteExisting?: boolean
  mergeMode?: 'replace' | 'merge' | 'skip'
  validateData?: boolean
}

/**
 * 导入结果
 */
export interface ImportResult {
  success: boolean
  importedNotes: number
  importedTags: number
  skippedNotes: number
  skippedTags: number
  errors: string[]
}
