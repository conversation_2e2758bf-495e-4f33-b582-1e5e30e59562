"use client"

import React, { useState } from "react"

interface AppIconProps {
  size?: number
  className?: string
  alt?: string
}

export function AppIcon({ size = 20, className = "", alt = "应用图标" }: AppIconProps) {
  const [imageError, setImageError] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  // 根据尺寸选择合适的图标
  const getIconSrc = () => {
    if (size <= 32) {
      return "/app-icon-32.png"
    } else if (size <= 64) {
      return "/app-icon-64.png"
    } else {
      return "/app-icon-64.png" // 对于更大的尺寸，使用最大的可用图标
    }
  }

  const handleImageError = () => {
    setImageError(true)
  }

  const handleImageLoad = () => {
    setIsLoaded(true)
  }

  if (imageError) {
    // 备用图标 - 使用渐变背景和 emoji
    return (
      <div 
        className={`bg-gradient-to-br from-blue-500 to-purple-600 rounded-sm flex items-center justify-center shadow-sm ${className}`}
        style={{ width: size, height: size }}
      >
        <span 
          className="text-white font-bold"
          style={{ fontSize: Math.max(8, size * 0.6) }}
        >
          📝
        </span>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`} style={{ width: size, height: size }}>
      <img
        src={getIconSrc()}
        alt={alt}
        className={`w-full h-full rounded-sm shadow-sm transition-opacity duration-200 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        onError={handleImageError}
        onLoad={handleImageLoad}
        style={{ width: size, height: size }}
      />
      
      {/* 加载占位符 */}
      {!isLoaded && !imageError && (
        <div 
          className="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded-sm animate-pulse"
          style={{ width: size, height: size }}
        />
      )}
    </div>
  )
}
