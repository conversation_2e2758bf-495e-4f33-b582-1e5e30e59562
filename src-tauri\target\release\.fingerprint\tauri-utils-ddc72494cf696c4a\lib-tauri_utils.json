{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 8943067380521412502, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 9108590051326616261], [3150220818285335163, "url", false, 996284761323975504], [3191507132440681679, "serde_untagged", false, 17882819573351763267], [4071963112282141418, "serde_with", false, 6437720419325518487], [4899080583175475170, "semver", false, 7470576787381197416], [5986029879202738730, "log", false, 8722473586600162715], [6606131838865521726, "ctor", false, 10102783720916590824], [7170110829644101142, "json_patch", false, 10654694494058036675], [8319709847752024821, "uuid", false, 5255073025142296482], [8569119365930580996, "serde_json", false, 12505152543355507582], [9010263965687315507, "http", false, 4601769349494712840], [9451456094439810778, "regex", false, 14858649163905654004], [9556762810601084293, "brotli", false, 11444421916978605795], [9689903380558560274, "serde", false, 13348221570411466918], [10806645703491011684, "thiserror", false, 16969897230673062756], [11989259058781683633, "dunce", false, 1794205478168231105], [13625485746686963219, "anyhow", false, 3571148104063441989], [15609422047640926750, "toml", false, 17176378821606956718], [15622660310229662834, "walkdir", false, 16358211513580219915], [15932120279885307830, "memchr", false, 11261746297635178691], [17146114186171651583, "infer", false, 7160465359863069230], [17155886227862585100, "glob", false, 12242844930303994883], [17186037756130803222, "phf", false, 4724397748947421202]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-ddc72494cf696c4a\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}