{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"sqlite\", \"time\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 1384594146405796144, "path": 16895545119748951556, "deps": [[3060637413840920116, "proc_macro2", false, 12829056815806260006], [4974441333307933176, "syn", false, 2939883612788580963], [10654871823602349891, "sqlx_macros_core", false, 1627960739365777400], [10776111606377762245, "sqlx_core", false, 13100294316342136224], [17990358020177143287, "quote", false, 7892140333893364853]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-3da48f7d5508af78\\dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}