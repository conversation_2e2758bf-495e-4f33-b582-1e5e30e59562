{"rustc": 1842507548689473721, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 11987593577772629573, "path": 18395026236560453371, "deps": [[2013030631243296465, "webview2_com", false, 14716284576735372477], [3334271191048661305, "windows_version", false, 12209608040518432003], [3722963349756955755, "once_cell", false, 15951163895981159829], [4143744114649553716, "raw_window_handle", false, 8871094038573090994], [5628259161083531273, "windows_core", false, 9093208648164683367], [7606335748176206944, "dpi", false, 10652289890363185665], [9010263965687315507, "http", false, 4609389182342060280], [9141053277961803901, "build_script_build", false, 2894616615554000859], [10806645703491011684, "thiserror", false, 4162130665435751246], [11989259058781683633, "dunce", false, 18087009893956605591], [14585479307175734061, "windows", false, 16572681052828898213], [16727543399706004146, "cookie", false, 3831476817164181169]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-c9577e9ec32818af\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}