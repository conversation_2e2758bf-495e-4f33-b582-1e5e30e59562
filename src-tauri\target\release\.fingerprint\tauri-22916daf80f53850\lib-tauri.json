{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 14661550995293435084, "deps": [[40386456601120721, "percent_encoding", false, 7931579284272468289], [654232091421095663, "tauri_utils", false, 13712148212591670789], [1200537532907108615, "url<PERSON><PERSON>n", false, 9108590051326616261], [2013030631243296465, "webview2_com", false, 14951264712843224681], [3150220818285335163, "url", false, 996284761323975504], [3331586631144870129, "getrandom", false, 10543716520641357618], [4143744114649553716, "raw_window_handle", false, 13056677237480622340], [4494683389616423722, "muda", false, 7767736146692827710], [4919829919303820331, "serialize_to_javascript", false, 1491884359628035015], [5986029879202738730, "log", false, 8722473586600162715], [8569119365930580996, "serde_json", false, 12505152543355507582], [9010263965687315507, "http", false, 4601769349494712840], [9689903380558560274, "serde", false, 13348221570411466918], [10229185211513642314, "mime", false, 7811686386244689245], [10806645703491011684, "thiserror", false, 16969897230673062756], [11989259058781683633, "dunce", false, 1794205478168231105], [12092653563678505622, "build_script_build", false, 1741793072285732969], [12304025191202589669, "tauri_runtime_wry", false, 17445677514938577408], [12393800526703971956, "tokio", false, 2773498000967960354], [12565293087094287914, "window_vibrancy", false, 6159255699248486825], [12943761728066819757, "tauri_runtime", false, 1628123000452858896], [12986574360607194341, "serde_repr", false, 3433130050631377470], [13077543566650298139, "heck", false, 14669962587389215398], [13405681745520956630, "tauri_macros", false, 15887008721102864325], [13625485746686963219, "anyhow", false, 3571148104063441989], [14585479307175734061, "windows", false, 3701177281461759728], [16928111194414003569, "dirs", false, 6034787275942488462], [17155886227862585100, "glob", false, 12242844930303994883]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-22916daf80f53850\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}